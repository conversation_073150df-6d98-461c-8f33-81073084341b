<?php
/**
 * Migration script to add os_id column to orders_items table
 * and populate it with OS information from orders table
 */

require_once 'mysql.php';

try {
    echo "Starting OS ID migration...\n";
    
    // Check if os_id column exists
    $checkColumn = $pdo->query("SHOW COLUMNS FROM orders_items LIKE 'os_id'");
    if ($checkColumn->rowCount() == 0) {
        echo "Adding os_id column to orders_items table...\n";
        $pdo->exec("ALTER TABLE orders_items ADD COLUMN os_id INT(11) NULL COMMENT 'Operating System ID from dedicated_os table' AFTER subnet_id");
        $pdo->exec("ALTER TABLE orders_items ADD KEY os_id (os_id)");
        echo "Column added successfully.\n";
    } else {
        echo "os_id column already exists.\n";
    }
    
    // Populate os_id from orders table where it's missing
    echo "Populating os_id values from orders table...\n";
    
    $updateQuery = "
        UPDATE orders_items oi 
        JOIN orders o ON oi.order_id = o.id 
        SET oi.os_id = o.os_id 
        WHERE oi.os_id IS NULL AND o.os_id IS NOT NULL
    ";
    
    $stmt = $pdo->prepare($updateQuery);
    $stmt->execute();
    $updatedRows = $stmt->rowCount();
    
    echo "Updated $updatedRows rows with OS ID information.\n";
    
    // Set default OS ID (Ubuntu) for remaining NULL values
    echo "Setting default OS ID for remaining NULL values...\n";
    
    $defaultOSQuery = "
        UPDATE orders_items 
        SET os_id = 1 
        WHERE os_id IS NULL
    ";
    
    $stmt = $pdo->prepare($defaultOSQuery);
    $stmt->execute();
    $defaultRows = $stmt->rowCount();
    
    echo "Set default OS ID for $defaultRows rows.\n";
    
    echo "Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
