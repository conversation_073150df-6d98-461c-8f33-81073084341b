import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Server,
  HardDrive,
  Cpu,
  EyeOff,
  Network,
  Settings,
  Wifi,
  Layers,
  Calendar,
  XCircle,
  RefreshCw,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Globe,
  MapPin,
  Activity,
  AlertTriangle,
  CheckCircle,
  Lock,
  Building,
  User,
  Check
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import StorageDetectionButton from '../components/StorageDetectionButton';
import BulkServerAddition from '../components/BulkServerAddition';
import MacAddressDetectionButton from '../components/MacAddressDetectionButton';
import PowerManagement from '../components/PowerManagement';
import SwitchPortsManager from '../components/SwitchPortsManager';
import SelectSubnetModal from '../components/SelectSubnetModal';
import NetworkConnections from '../components/NetworkConnections';
import CountryIpSelector from '../components/CountryIpSelector';
import { API_URL } from '../config';
// Legacy IpmiSelector component replaced with CountryIpSelector

const InventoryPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for inventory data
  const [bladeServers, setBladeServers] = useState([]);
  const [dedicatedServers, setDedicatedServers] = useState([]);
  const [chassis, setChassis] = useState([]);
  const [switches, setSwitches] = useState([]);
  // Add near other state declarations (around line 70)
  const [switchModels, setSwitchModels] = useState([]);
  const [showAddSwitchModelModal, setShowAddSwitchModelModal] = useState(false);
  const [newSwitchModel, setNewSwitchModel] = useState({ name: '', size: 1 });
  // State for related data
  const [storage, setStorage] = useState([]);
  const [racks, setRacks] = useState([]);
  const [cities, setCities] = useState([]);
  const [countries, setCountries] = useState([]);
  const [portAssignmentModalOpen, setPortAssignmentModalOpen] = useState(false);
  const [availablePorts, setAvailablePorts] = useState({});
  // State for UI interactions
  const [selectedTab, setSelectedTab] = useState('dedicated');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedLocation, setSelectedLocation] = useState('All');
  const [sortField, setSortField] = useState('id');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedItem, setSelectedItem] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [portsRefreshKey, setPortsRefreshKey] = useState(Date.now());
  // State for modals
  const [addItemModalOpen, setAddItemModalOpen] = useState(false);
  const [editItemModalOpen, setEditItemModalOpen] = useState(false);
  const [visiblePasswords, setVisiblePasswords] = useState({});

  const [isSubnetSelectionModalOpen, setIsSubnetSelectionModalOpen] = useState(false);
  const [isSelectingMainSubnet, setIsSelectingMainSubnet] = useState(true);

  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('success'); // 'success' or 'error'

  // Add port manually states
  const [showAddPortModal, setShowAddPortModal] = useState(false);
  const [newPortData, setNewPortData] = useState({
    port_number: '',
    port_name: '',
    max_speed: '10000', // Default to 10Gbps
  });
  const [addingPort, setAddingPort] = useState(false);

  const [ramConfigurations, setRamConfigurations] = useState([]);
  const [cpuModels, setCpuModels] = useState([]);
  const [showAddCpuModal, setShowAddCpuModal] = useState(false);
  const [newCpuModel, setNewCpuModel] = useState('');
  const [showAddRamModal, setShowAddRamModal] = useState(false);
  const [newRamConfig, setNewRamConfig] = useState({ size: '', description: '' });

  const [storageModalData, setStorageModalData] = useState(null); // {disks:[], x, y}

// Add near other handler functions (around line 300-400)
const handleOpenPortAssignmentModal = () => {
  setPortAssignmentModalOpen(true);
};
// Function to fetch available ports for a switch
const fetchAvailablePorts = async (switchId) => {
  if (!switchId) return;

  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        switch_id: switchId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();

    // Filter only available ports
    const availablePortsForSwitch = data.filter(port => port.status === 'Available');

    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: availablePortsForSwitch
    }));

    setLoading(false);
  } catch (error) {
    console.error("Error fetching available ports:", error);
    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: []
    }));
    setLoading(false);
  }
};

const handleRemoveAdditionalSubnet = (indexToRemove) => {
  if (!selectedItem || !selectedItem.additional_ips) return;

  // Get current IPs as an array
  const ips = selectedItem.additional_ips.split(',').map(ip => ip.trim());

  // Remove the specified IP
  const updatedIps = ips.filter((_, index) => index !== indexToRemove);

  // Update selected item state
  const updatedItem = { ...selectedItem };
  updatedItem.additional_ips = updatedIps.join(', ');
  setSelectedItem(updatedItem);

  // Update the list based on server type
  if (selectedTab === 'dedicated') {
    setDedicatedServers(prevServers =>
      prevServers.map(server =>
        server.id === updatedItem.id ? updatedItem : server
      )
    );
  } else if (selectedTab === 'blade') {
    setBladeServers(prevServers =>
      prevServers.map(server =>
        server.id === updatedItem.id ? updatedItem : server
      )
    );
  }
};

const showToast = (message, type = 'success') => {
  setToastMessage(message);
  setToastType(type);
  
  // Auto-hide after 3 seconds
  setTimeout(() => {
    setToastMessage('');
  }, 3000);
};

const handleClosePortAssignmentModal = () => {
  setPortAssignmentModalOpen(false);
};


const handleUnallocateSubnet = async (subnetCidr, isMainSubnet) => {
  console.log('🔄 handleUnallocateSubnet called with:', { subnetCidr, isMainSubnet });
  
  // Prevent multiple simultaneous calls
  if (window.unallocationInProgress) {
    console.log('⚠️ Unallocation already in progress, ignoring click');
    return;
  }
  
  try {
    window.unallocationInProgress = true;
    
    // Confirm before unallocating
    const confirmUnallocate = window.confirm(`Are you sure you want to unallocate the subnet ${subnetCidr}?`);
    if (!confirmUnallocate) {
      console.log('❌ User cancelled unallocation');
      window.unallocationInProgress = false;
      return;
    }
    
    console.log('🚀 Starting unallocation process...');
    
    // Update UI immediately (optimistic)
    const updatedItem = { ...selectedItem };
    if (isMainSubnet) {
      // When unallocating main subnet, check if we have additional subnets
      const additionalIps = updatedItem.additional_ips 
        ? updatedItem.additional_ips.split(',').map(ip => ip.trim()).filter(ip => ip)
        : [];
      
      if (additionalIps.length > 0) {
        // Promote the first additional subnet to main
        updatedItem.main_ip = additionalIps[0];
        // Remove the promoted subnet from additional IPs
        const remainingIps = additionalIps.slice(1);
        updatedItem.additional_ips = remainingIps.length > 0 ? remainingIps.join(', ') : '';
      } else {
        // No additional subnets, just clear main IP
        updatedItem.main_ip = null;
      }
    } else {
      // Remove from additional IPs
      const additionalIps = updatedItem.additional_ips 
        ? updatedItem.additional_ips.split(',').map(ip => ip.trim())
        : [];
      const filteredIps = additionalIps.filter(ip => ip !== subnetCidr);
      updatedItem.additional_ips = filteredIps.length > 0 ? filteredIps.join(', ') : '';
    }
    
    // Force UI update with new reference
    setSelectedItem(updatedItem);
    
    // Update the list based on server type
    if (selectedTab === 'dedicated') {
      setDedicatedServers(prevServers =>
        prevServers.map(server =>
          server.id === updatedItem.id ? updatedItem : server
        )
      );
    } else if (selectedTab === 'blade') {
      setBladeServers(prevServers =>
        prevServers.map(server =>
          server.id === updatedItem.id ? updatedItem : server
        )
      );
    }
    
    console.log('✅ Updated UI immediately (optimistic)');
    
    // Show success message immediately
    alert('✅ Subnet unallocated successfully! Switch unconfiguration and ACL removal will continue in background.');
    
    const token = localStorage.getItem('admin_token');
    if (!token) {
      throw new Error('No authentication token found');
    }
    
    // Make the API call in background
    console.log(`📡 Making API call in background for regular unallocation: ${subnetCidr} (promotion enabled)`);
    
    const abortController = new AbortController();
    setTimeout(() => abortController.abort(), 10000); // 10 second timeout
    
    fetch(`${API_URL}/api_admin_subnets.php?f=unallocate_subnet_server_side`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        subnet_cidr: subnetCidr
        // Note: skip_promotion not passed, so defaults to false (promotion enabled)
      }),
      signal: abortController.signal
    })
    .then(response => response.json())
    .then(data => {
      console.log('📡 Background API response:', data);
      if (data.success) {
        console.log('✅ Subnet unallocated successfully in background');
      } else {
        console.error('❌ Background unallocation failed:', data.error);
        // Don't show alert for background failures, just log them
      }
    })
    .catch(error => {
      console.error('❌ Background unallocation network error:', error);
      // Don't show alert for background operations to avoid blocking user
    });

  } catch (err) {
    console.error('❌ Error in handleUnallocateSubnet:', err);
    alert(`❌ Error: ${err.message}`);
  } finally {
    window.unallocationInProgress = false;
    console.log('🏁 Unallocation process completed');
  }
};

const handleSubnetSelection = async (subnet) => {
  try {
    // Store the previous main subnet for unallocation if we're assigning a new main subnet
    const previousMainSubnet = isSelectingMainSubnet ? selectedItem?.main_ip : null;
    
    console.log(`🔄 Subnet selection - Mode: ${isSelectingMainSubnet ? 'Main Subnet' : 'Additional Subnet'}`);
    console.log(`📋 Current state - Main IP: ${selectedItem?.main_ip}, Additional IPs: ${selectedItem?.additional_ips}`);
    console.log(`🎯 New subnet to assign: ${subnet.cidr}`);
    console.log(`🗑️ Previous main subnet to unallocate: ${previousMainSubnet || 'None'}`);
    
    // Update UI immediately (optimistic)
    const updatedItem = { ...selectedItem };
    if (isSelectingMainSubnet) {
      // Changing main subnet - only update main_ip, keep additional_ips unchanged
      updatedItem.main_ip = subnet.cidr;
      console.log(`✏️ UI Update: Setting main_ip to ${subnet.cidr}, keeping additional_ips as: ${updatedItem.additional_ips}`);
    } else {
      // Adding additional subnet - only update additional_ips, keep main_ip unchanged
      const currentIps = updatedItem.additional_ips
        ? updatedItem.additional_ips.split(',').map(ip => ip.trim())
        : [];
      if (!currentIps.includes(subnet.cidr)) {
        currentIps.push(subnet.cidr);
        updatedItem.additional_ips = currentIps.join(', ');
        console.log(`✏️ UI Update: Adding to additional_ips: ${updatedItem.additional_ips}, keeping main_ip as: ${updatedItem.main_ip}`);
      }
    }
    
    // Force UI update
    setSelectedItem(updatedItem);
    
    // Update the list based on server type
    if (selectedTab === 'dedicated') {
      setDedicatedServers(prevServers =>
        prevServers.map(server =>
          server.id === updatedItem.id ? updatedItem : server
        )
      );
    } else if (selectedTab === 'blade') {
      setBladeServers(prevServers =>
        prevServers.map(server =>
          server.id === updatedItem.id ? updatedItem : server
        )
      );
    }
    
    // Show success message immediately
    alert(isSelectingMainSubnet 
      ? '🎯 Main subnet changed! Previous main subnet will be unallocated, new subnet set as main IP, additional subnets remain unchanged. Switch configuration will run in background.' 
      : '➕ Additional subnet added! Switch configuration and ACL creation will run in background.');
    
    // Close the modal
    setIsSubnetSelectionModalOpen(false);
    
    const token = localStorage.getItem('admin_token');
    
    // If we're assigning a new main subnet and there was a previous main subnet, unallocate it first
    if (isSelectingMainSubnet && previousMainSubnet && previousMainSubnet !== subnet.cidr) {
      console.log(`🔄 Unallocating previous main subnet: ${previousMainSubnet} (subnet swap - skip promotion)`);
      
      // Fire-and-forget unallocation call for the previous main subnet (with timeout)
      const abortController = new AbortController();
      setTimeout(() => abortController.abort(), 10000); // 10 second timeout
      
      fetch(`${API_URL}/api_admin_subnets.php?f=unallocate_subnet_server_side`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          subnet_cidr: previousMainSubnet,
          skip_promotion: true  // Skip promotion since we're swapping, not just unallocating
        }),
        signal: abortController.signal
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          console.log('✅ Previous main subnet unallocated successfully in background');
        } else {
          console.error('❌ Previous main subnet unallocation failed:', data.error);
        }
      })
      .catch(error => {
        console.error('❌ Previous main subnet unallocation network error:', error);
      });
    }
    
    // Send request to assign new subnet (background, no await)
    const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
      ? subnet.id : subnet.id;

    const assignmentPayload = {
      token,
      subnet_id: subnetId,
      server_id: selectedItem.id,
      server_type: selectedTab,
      set_as_main_ip: isSelectingMainSubnet
    };
    
    console.log(`📡 API Call: assign_subnet_to_server with payload:`, assignmentPayload);

    // Simple retry mechanism for assignment (more critical operation)
    const attemptAssignment = async (retries = 2) => {
      try {
        const response = await fetch(`${API_URL}/api_admin_subnets.php?f=assign_subnet_to_server`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(assignmentPayload)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          console.log('✅ New subnet assigned successfully in background');
          console.log('🔧 Switch configuration and ACL creation handled automatically by assign_subnet_to_server API');
        } else {
          console.error('❌ Background assignment failed:', data.error);
          // Only show alert for API errors (not network errors)
          alert(`Background assignment failed: ${data.error}`);
        }
      } catch (error) {
        console.error(`❌ Network error on assignment attempt (${3-retries}/3):`, error);
        
        if (retries > 0) {
          console.log(`🔄 Retrying assignment in 1 second... (${retries} attempts left)`);
          setTimeout(() => attemptAssignment(retries - 1), 1000);
        } else {
          console.error('❌ All assignment attempts failed - operation completed optimistically in UI');
          // Don't show alert - user already sees optimistic UI update
        }
      }
    };
    
    // Start the assignment with retries
    attemptAssignment();

  } catch (err) {
    console.error('Error:', err);
    alert(`Error: ${err.message}`);
  }
};


const handlePortAssignmentComplete = () => {
  // Refresh the server data
  if (selectedTab === 'dedicated') {
    fetchDedicatedServers();
  } else if (selectedTab === 'blade') {
    fetchBladeServers();
  } else if (selectedTab === 'switch') {
    fetchSwitches();
  }

  // Close the modal after a short delay
  setTimeout(() => {
    setPortAssignmentModalOpen(false);
  }, 2000);
};

// SNMP Configuration Component for Switch Details
const SnmpConfigurationSection = ({
  isEditMode,
  selectedItem,
  handleEditItemChange,
  discoverSwitchPorts
}) => {
  // Default SNMP versions
  const snmpVersions = [
    { value: 1, label: 'SNMP v1' },
    { value: 2, label: 'SNMP v2c' },
    { value: 3, label: 'SNMP v3' }
  ];

  // Function to handle port discovery when SNMP is configured
  const handleDiscoverPorts = () => {
    if (selectedItem.snmp_community) {
      discoverSwitchPorts(
        selectedItem.id,
        selectedItem.snmp_community,
        selectedItem.snmp_version || 2
      );
    } else {
      // Prompt user for community string if not saved
      const community = prompt('Enter SNMP community string:', 'public');
      if (community) {
        discoverSwitchPorts(selectedItem.id, community, selectedItem.snmp_version || 2);

        // Force ports refresh by emitting the event directly
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType: 'switch',
            switchId: selectedItem.id,
            timestamp: Date.now(),
            action: 'port-discovery'
          }
        });
        window.dispatchEvent(event);
      }
    }
  };

  return (
    <div className="space-y-3">
      {/* SNMP Community String */}
      <div>
        <div className="text-xs text-gray-500">Community String</div>
        {isEditMode ? (
          <input
            type="text"
            name="snmp_community"
            value={selectedItem.snmp_community || ''}
            onChange={handleEditItemChange}
            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            placeholder="e.g. public"
          />
        ) : (
          <div className="font-medium flex items-center">
            {selectedItem.snmp_community ? (
              <>
                {selectedItem.snmp_community}
              </>
            ) : (
              <span className="text-gray-500">Not configured</span>
            )}
          </div>
        )}
      </div>

      {/* SNMP Version */}
      <div>
        <div className="text-xs text-gray-500">SNMP Version</div>
        {isEditMode ? (
          <select
            name="snmp_version"
            value={selectedItem.snmp_version || 2}
            onChange={handleEditItemChange}
            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            {snmpVersions.map(version => (
              <option key={version.value} value={version.value}>
                {version.label}
              </option>
            ))}
          </select>
        ) : (
          <div className="font-medium">
            {selectedItem.snmp_version ?
              `SNMP v${selectedItem.snmp_version}` :
              'Default (SNMPv2c)'}
          </div>
        )}
      </div>


      {/* Discover Ports Button - Only show in view mode and when SNMP is configured */}
      {!isEditMode && (
        <div className="mt-2">
          <button
            onClick={handleDiscoverPorts}
            className="flex items-center px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded text-xs transition-colors"
          >
            <Network className="w-3 h-3 mr-1" />
            Discover Ports
          </button>
          {!selectedItem.snmp_community && (
            <div className="mt-1 text-xs text-gray-500">
              Note: You will be prompted for SNMP credentials if not saved.
            </div>
          )}
        </div>
      )}
    </div>
  );
};




const discoverSwitchPorts = async (switchId, community, version = 2) => {
  try {
    const token = localStorage.getItem('admin_token');

    // Show loading
    setLoading(true);

    // Make API call
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=discover_switch_ports`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        switch_id: switchId,
        snmp_community: community,
        snmp_version: version
      })
    });

    // Handle response
    const textResponse = await response.text();
    let result;

    try {
      result = JSON.parse(textResponse);
    } catch {
      throw new Error('Server response error');
    }

    if (!result.success) {
      throw new Error(result.error || 'Discovery failed');
    }

    // Update UI
    alert(`Found ${result.ports_detected} ports!`);
    // Refresh switch ports in UI
    await fetchSwitchPorts(switchId);

    // Set ports refresh key to force component remount
    setPortsRefreshKey(Date.now());

    // Emit an event to refresh components
    const event = new CustomEvent('inventory-data-change', {
      detail: {
        deviceType: 'switch',
        switchId: switchId,
        timestamp: Date.now(),
        action: 'port-discovery' // Ensure consistent naming of the action
      }
    });
    window.dispatchEvent(event);

  } catch (error) {
    alert(`Error: ${error.message}\n\nCheck:\n1. SNMP Community String\n2. Switch IP\n3. Network Connection`);
  } finally {
    setLoading(false);
  }
};





const fetchSwitchPorts = async (switchId) => {
  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        switch_id: switchId
      })
    });

    // Handle non-JSON responses
    const responseText = await response.text();
    let ports;

    try {
      ports = JSON.parse(responseText);
    } catch (e) {
      console.error('Invalid JSON response:', responseText);
      throw new Error('Invalid server response format');
    }

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: ports.filter(port => port.status === 'Available')
    }));

    setLoading(false);
    return ports;
  } catch (err) {
    setLoading(false);
    console.error("Error fetching switch ports:", err);
    alert(`Failed to fetch ports: ${err.message}`);
    return [];
  }
};





  // State for storage configs and bulk add
  const storageConfigurations = [
    { id: 1, size: 240, label: '240GB SSD' },
    { id: 2, size: 500, label: '500GB SSD' },
    { id: 3, size: 1000, label: '1TB SSD' },
    { id: 4, size: 2000, label: '2TB SSD' },
    { id: 5, size: 4000, label: '4TB SSD' },
    { id: 6, size: 6000, label: '6TB SSD' },
    { id: 7, size: 8000, label: '8TB SSD' },
    { id: 8, size: 10000, label: '10TB HDD' },
    { id: 9, size: 12000, label: '12TB HDD' }
  ];

  const [bulkAddModalOpen, setBulkAddModalOpen] = useState(false);

  // State for new item
  const [newItem, setNewItem] = useState({
    label: '',
    cpu: '',
    switch: '',
    port1: '',
    port1_speed: '',
    port2: '',
    port2_speed: '',
    port3: '',
    port3_speed: '',
    port4: '',
    port4_speed: '',
    bay1: '',
    bay2: '',
    bay3: '',
    bay4: '',
    bay5: '',
    bay6: '',
    bay7: '',
    bay8: '',
    bay9: '',
    bay10: '',
    mac: '',
    ipmi: '',
    rootUser: '',
    notes: '',
    main_ip: '',
    additional_ips: '',
    rack_position: '', // Add this field!
    position: '',      // Keep both position fields
    switch_ip: '',     // Add other switch-specific fields
    root_password: '',
    size_ru: ''
  });

  // State for edit item
  const [editItem, setEditItem] = useState(null);

  // State for locations and status options
  const [uniqueLocations, setUniqueLocations] = useState(['All']);
  // Begin dynamic Location list updater
  useEffect(() => {
    let items = [];
    switch (selectedTab) {
      case 'dedicated':
        items = dedicatedServers;
        break;
      case 'blade':
        items = bladeServers;
        break;
      case 'chassis':
        items = chassis;
        break;
      case 'switch':
        items = switches;
        break;
      default:
        items = [];
    }

    const cityCountryMap = new Map(); // city -> preferred country

    items.forEach((itm) => {
      const city = itm.city_name || 'Unknown';
      const country = itm.country_name || 'Unknown';

      // If we already stored this city with a real country, keep it.
      if (cityCountryMap.has(city)) {
        const existingCountry = cityCountryMap.get(city);
        if (existingCountry === 'Unknown' && country !== 'Unknown') {
          cityCountryMap.set(city, country);
        }
      } else {
        cityCountryMap.set(city, country);
      }
    });

    const locArray = ['All', ...Array.from(cityCountryMap.entries()).map(([city, country]) => `${city}, ${country}`)];
    setUniqueLocations(locArray);
  }, [selectedTab, dedicatedServers, bladeServers, chassis, switches]);
  // End dynamic Location list updater
  const [uniqueStatuses, setUniqueStatuses] = useState(['All', 'Available', 'Active', 'In use', 'Defect']);
  // CPU filter state
  const [uniqueCpus, setUniqueCpus] = useState(['All']);
  const [selectedCpuModel, setSelectedCpuModel] = useState('All');
  // Begin dynamic CPU list updater
  useEffect(() => {
    let items = [];
    switch (selectedTab) {
      case 'dedicated':
        items = dedicatedServers;
        break;
      case 'blade':
        items = bladeServers;
        break;
      default:
        items = [];
    }

    const cpuSet = new Set(['All']);
    items.forEach((itm) => {
      const cpuName = (itm.cpu_name || itm.cpu || 'Unknown').trim();
      if (cpuName) cpuSet.add(cpuName);
    });

    setUniqueCpus(Array.from(cpuSet));
  }, [selectedTab, dedicatedServers, bladeServers]);
  // End dynamic CPU list updater

  // Stats for inventory overview
  const [inventoryStats, setInventoryStats] = useState({
    totalDedicated: 0,
    totalBlades: 0,
    totalChassis: 0,
    totalSwitches: 0,
    activeServers: 0,
    defectServers: 0,
    inuseServers: 0,
    storageCapacity: 0
  });

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  // Update MAC address for a server
  const handleUpdateMac = async (macAddress) => {
    try {
      if (!macAddress || !selectedItem || !selectedItem.id) {
        alert('Invalid MAC address or server');
        return;
      }

      console.log(`Updating MAC address to: ${macAddress} for ${selectedTab} server ID: ${selectedItem.id}`);

      // Create the updates object
      const updatePayload = {
        id: selectedItem.id,
        mac: macAddress
      };

      // Use the correct API endpoint based on server type
      const endpoint = selectedTab === 'dedicated'
        ? 'update_dedicated_server'
        : 'update_blade_server_inventory';

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ...updatePayload
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API response error:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log("API response:", result);

      if (result.success) {
        // Update the UI state with the new MAC address
        const updatedServer = { ...selectedItem, mac: macAddress };

        // Update selected item
        setSelectedItem(updatedServer);

        // Update the list based on server type
        if (selectedTab === 'dedicated') {
          setDedicatedServers(prevServers =>
            prevServers.map(server =>
              server.id === updatedServer.id ? updatedServer : server
            )
          );
        } else {
          setBladeServers(prevServers =>
            prevServers.map(server =>
              server.id === updatedServer.id ? updatedServer : server
            )
          );
        }

        // Force UI refresh
        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
      } else {
        // Handle error
        let errorMessage = 'Failed to update MAC address';
        if (result.error) {
          errorMessage += `: ${result.error}`;
        }
        if (result.message) {
          errorMessage += ` (${result.message})`;
        }
        alert(errorMessage);
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error updating MAC address:", err);
      alert(`Failed to update MAC address: ${err.message}`);
    }
  };

// Add near the other fetch functions (around line 250-300)
// Fetch switch models
const fetchSwitchModels = async () => {
  try {
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switch_models`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();

    if (Array.isArray(data)) {
      setSwitchModels(data);
      console.log(`Loaded ${data.length} switch models`);
    } else {
      setSwitchModels([]);
    }
  } catch (err) {
    console.error("Error fetching switch models:", err);
    setSwitchModels([]);
  }
};

// Add near other handler functions (around line 300-350)
// Add a new switch model
const handleAddSwitchModel = async () => {
  try {
    if (!newSwitchModel.name.trim()) {
      alert('Switch model name is required');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_switch_model`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        name: newSwitchModel.name,
        size: newSwitchModel.size
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      setShowAddSwitchModelModal(false);
      setNewSwitchModel({ name: '', size: 1 });

      // Refresh switch models
      await fetchSwitchModels();

      alert('Switch model added successfully');
    } else {
      alert(result.error || 'Failed to add switch model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding switch model:", err);
    alert('Failed to add switch model: ' + err.message);
  }
};

  const fetchRamConfigurations = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setRamConfigurations(data);
        console.log(`Loaded ${data.length} RAM configurations`);
      } else {
        setRamConfigurations([]);
      }
    } catch (err) {
      console.error("Error fetching RAM configurations:", err);
      setRamConfigurations([]);
    }
  };


// Fetch CPU models
const fetchCpuModels = async () => {
  try {
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();

    if (Array.isArray(data)) {
      setCpuModels(data);
      console.log(`Loaded ${data.length} CPU models`);
    } else {
      setCpuModels([]);
    }
  } catch (err) {
    console.error("Error fetching CPU models:", err);
    setCpuModels([]);
  }
};



// Add a new CPU model
const handleAddCpuModel = async () => {
  try {
    if (!newCpuModel.trim()) {
      alert('CPU model name is required');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_cpu_model`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        cpu: newCpuModel
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      setShowAddCpuModal(false);
      setNewCpuModel('');

      // Refresh CPU models
      await fetchCpuModels();

      alert('CPU model added successfully');
    } else {
      alert(result.error || 'Failed to add CPU model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding CPU model:", err);
    alert('Failed to add CPU model: ' + err.message);
  }
};


const handleAddRamConfig = async () => {
  try {
    if (!newRamConfig.size || !newRamConfig.description) {
      alert('RAM size and description are required');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_ram_configuration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        size: newRamConfig.size,
        description: newRamConfig.description
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      setShowAddRamModal(false);
      setNewRamConfig({ size: '', description: '' });

      // Refresh RAM configurations
      await fetchRamConfigurations();

      alert('RAM configuration added successfully');
    } else {
      alert(result.error || 'Failed to add RAM configuration');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding RAM configuration:", err);
    alert('Failed to add RAM configuration: ' + err.message);
  }
};



  // Update storage configuration
  const handleUpdateStorage = async (bayUpdates) => {
    try {
      if (!bayUpdates || Object.keys(bayUpdates).length === 0) {
        alert('No storage changes to apply');
        return;
      }

      if (!bayUpdates.id) {
        if (!selectedItem || !selectedItem.id) {
          alert('No server selected');
          return;
        }
        bayUpdates.id = selectedItem.id;
      }

      // Get server type (correctly handle it as a parameter rather than a field to update)
      const serverType = bayUpdates.serverType || (selectedTab === 'dedicated' ? 'dedicated' : 'blade');
      delete bayUpdates.serverType; // Remove it so it doesn't get sent as a field to update

      console.log(`Updating storage configuration for ${serverType} server ID:`, bayUpdates.id);

      // Determine the maximum number of bays based on server type
      const maxBays = serverType === 'dedicated' ? 26 : 10;

      // Create the updates object with bay fields
      const updatePayload = { id: bayUpdates.id };
      let hasAnyChanges = false;

      // Filter only bay fields and check if there are any actual changes
      for (const [key, value] of Object.entries(bayUpdates)) {
        if (key.startsWith('bay') && key !== 'id') {
          updatePayload[key] = value;
          hasAnyChanges = true;
        }
      }

      if (!hasAnyChanges) {
        console.log("No storage changes to update");
        alert("No storage changes detected");
        return;
      }

      // WORKAROUND: The API doesn't include bay fields in its allowed fields list
      // For dedicated servers, we need to include an allowed field to prevent SQL error
      if (serverType === 'dedicated') {
        // Include a dummy field update that's in the API's allowed list
        // This ensures the SQL query won't have an empty SET clause
        updatePayload.notes = selectedItem.notes || '';
      }

      console.log("Final update payload:", updatePayload);

      // Use the correct API endpoint based on server type
      const endpoint = serverType === 'dedicated'
        ? 'update_dedicated_server'
        : 'update_blade_server_inventory';

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ...updatePayload
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API response error:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log("API response:", result);

      if (result.success) {
        // Update the UI state with the new configuration
        const updatedServer = { ...selectedItem };

        // Update all bay fields
        for (const [key, value] of Object.entries(bayUpdates)) {
          if (key.startsWith('bay') && key !== 'id') {
            updatedServer[key] = value;

            // Add bay name properties for UI display
            if (value && value !== '0') {
              const storageConfig = storageConfigurations.find(config =>
                String(config.id) === String(value)
              );
              if (storageConfig) {
                updatedServer[`${key}_name`] = storageConfig.label;
              }
            } else {
              updatedServer[`${key}_name`] = '';
            }
          }
        }

        // Remove the notes timestamp if we added it as a workaround
        if (serverType === 'dedicated' && selectedItem.notes !== updatePayload.notes) {
          // Restore the original notes
          updatedServer.notes = selectedItem.notes;
        }

        // Update selected item
        setSelectedItem(updatedServer);

        // Update the list based on server type
        if (serverType === 'dedicated') {
          setDedicatedServers(prevServers =>
            prevServers.map(server =>
              server.id === updatedServer.id ? updatedServer : server
            )
          );
        } else {
          setBladeServers(prevServers =>
            prevServers.map(server =>
              server.id === updatedServer.id ? updatedServer : server
            )
          );
        }

        // Force UI refresh
        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        alert("Storage configuration updated successfully!");
      } else {
        // Handle error
        let errorMessage = 'Failed to update storage configuration';
        if (result.error) {
          errorMessage += `: ${result.error}`;
        }
        if (result.message) {
          errorMessage += ` (${result.message})`;
        }
        alert(errorMessage);
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error updating storage configuration:", err);
      alert(`Failed to update storage: ${err.message}`);
    }
  };

  // Password Field Component
  const PasswordField = ({
    isEditMode,
    fieldName,
    value,
    onChange,
    placeholder = "••••••••",
    label
  }) => {
    const isVisible = visiblePasswords[fieldName] || false;

    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="relative">
            <input
              type={isVisible ? "text" : "password"}
              name={fieldName}
              value={value || ''}
              onChange={(e) => {
                onChange(e);
              }}
              className="font-medium w-full px-2 py-1 pr-8 border border-gray-300 rounded-md text-sm"
              placeholder={placeholder}
            />
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
              onClick={(e) => {
                e.preventDefault();
                togglePasswordVisibility(fieldName);
              }}
            >
              {isVisible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Lock className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value ? (
              <div className="flex items-center">
                <span>{isVisible ? value : placeholder}</span>
                <button
                  type="button"
                  className="ml-2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(fieldName)}
                >
                  {isVisible ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>
            ) : (
              'Not set'
            )}
          </div>
        )}
      </div>
    );
  };

  // Get country flag emoji
  const getCountryFlag = (countryName) => {
    // Map of country names to ISO country codes
    const countryCodeMap = {
      // European Countries
      'Albania': 'AL',
      'Andorra': 'AD',
      'Austria': 'AT',
      'Belarus': 'BY',
      'Belgium': 'BE',
      'Bosnia and Herzegovina': 'BA',
      'Bulgaria': 'BG',
      'Croatia': 'HR',
      'Cyprus': 'CY',
      'Czech Republic': 'CZ',
      'Denmark': 'DK',
      'Estonia': 'EE',
      'Finland': 'FI',
      'France': 'FR',
      'Germany': 'DE',
      'Greece': 'GR',
      'Hungary': 'HU',
      'Iceland': 'IS',
      'Ireland': 'IE',
      'Italy': 'IT',
      'Latvia': 'LV',
      'Liechtenstein': 'LI',
      'Lithuania': 'LT',
      'Luxembourg': 'LU',
      'Malta': 'MT',
      'Moldova': 'MD',
      'Monaco': 'MC',
      'Montenegro': 'ME',
      'Netherlands': 'NL',
      'North Macedonia': 'MK',
      'Norway': 'NO',
      'Poland': 'PL',
      'Portugal': 'PT',
      'Romania': 'RO',
      'Russia': 'RU',
      'San Marino': 'SM',
      'Serbia': 'RS',
      'Slovakia': 'SK',
      'Slovenia': 'SI',
      'Spain': 'ES',
      'Sweden': 'SE',
      'Switzerland': 'CH',
      'Ukraine': 'UA',
      'United Kingdom': 'GB',
      'UK': 'GB',
      'Vatican City': 'VA',

      // Other Major Countries
      'United States': 'US',
      'USA': 'US',
      'Canada': 'CA',
      'Australia': 'AU',
      'Japan': 'JP',
      'China': 'CN',
      'Singapore': 'SG',
      'South Korea': 'KR',
      'India': 'IN',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'South Africa': 'ZA'
    };

    // Resolve country code
    const countryCode = countryCodeMap[countryName] || '';
    if (!countryCode) return null;

    // Always return an <img> so that flags render consistently on all platforms
    // PNG assets are provided free by flagcdn.com (16x12 size keeps table rows compact)
    return (
      <img
        src={`https://flagcdn.com/16x12/${countryCode.toLowerCase()}.png`}
        alt={`${countryName} flag`}
        className="inline-block w-4 h-3 object-contain align-text-bottom"
        style={{ position: 'relative', top: '-2px' }}
      />
    );
  };



  // Handler for editing field values
  // IP allocation functions for IPMI management
  const allocateIp = async (ipAddress, deviceType, deviceId, deviceLabel) => {
    if (!ipAddress) return false;

    console.log(`Allocating ${ipAddress} for ${deviceType} ${deviceId || 'new'} (${deviceLabel})`);

    try {
      const token = localStorage.getItem('admin_token');

      // First, find the subnet ID for this IP
      const subnetsResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const subnets = await subnetsResponse.json();

      if (!Array.isArray(subnets)) {
        console.error('Failed to fetch subnets');
        return false;
      }

      // Search for the IP in each subnet
      let foundSubnet = false;
      let foundSubnetId = null;

      for (const subnet of subnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token
            })
          });

          const ipData = await ipResponse.json();

          if (ipData && ipData.success) {
            // Check if the IP exists in this subnet
            const ipInfo = ipData.ips.find(ip => ip.ip_address === ipAddress);

            if (ipInfo) {
              foundSubnet = true;
              foundSubnetId = subnetId;
              console.log(`Found IP ${ipAddress} in subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);
              break;
            }
          }
        } catch (err) {
          console.error(`Error checking subnet ${subnet.id}:`, err);
        }
      }

      if (foundSubnet && foundSubnetId) {
        // Create description and notes - assigned_to should only contain the device ID
        const isServer = deviceType === 'server';
        const isSwitch = deviceType === 'switch';

        // assigned_to field contains only the device ID for clean data management
        const manual_alocation = deviceId.toString();
        
        // notes field can contain descriptive information
        const notes = isServer ? `IPMI for ${deviceLabel} (ID: ${deviceId})` : `${deviceType === 'switch' ? 'Switch' : 'Device'} ${deviceLabel} (ID: ${deviceId})`;

        console.log(`Setting assigned_to: "${manual_alocation}" and notes: "${notes}" for IP ${ipAddress}`);

        // First, try to deallocate the IP if it's already in use
        try {
          console.log(`Deallocating IP ${ipAddress} before allocation`);

          const deallocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token,
              ip_address: ipAddress,
              for_server_ipmi: deviceType === 'server',
              for_switch: deviceType === 'switch'
            })
          });

          const deallocateResult = await deallocateResponse.json();

          if (deallocateResult.success) {
            console.log(`Successfully deallocated IP ${ipAddress}`);
          } else {
            console.log(`IP ${ipAddress} was not allocated or deallocation failed:`, deallocateResult.error || 'Unknown error');
          }
        } catch (err) {
          console.error(`Error deallocating IP ${ipAddress}:`, err);
        }

        // Now allocate the IP
        console.log(`Allocating IP ${ipAddress} in subnet ${foundSubnetId} for ${deviceType} ${deviceId || 'new'}`);

        // Prepare API request data
        const requestData = {
          token: token,
          subnet_id: foundSubnetId,
          ip_address: ipAddress,
          manual_alocation: manual_alocation,
          notes: notes,
          for_server_ipmi: deviceType === 'server',
          for_switch: deviceType === 'switch',
          server_id: deviceType === 'server' ? deviceId : undefined,
          switch_id: deviceType === 'switch' ? deviceId : undefined
        };

        // Add server_type if this is a server
        if (deviceType === 'server') {
          const serverType = selectedTab === 'blade' ? 'blade' : 'dedicated';
          requestData.server_type = serverType;
        }

        console.log('Sending IP allocation request:', requestData);

        // Call the API to allocate the IP
        let allocateResult = { success: false, error: 'Unknown error' };

        try {
          const allocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=allocate_ip`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
          });

          allocateResult = await allocateResponse.json();
        } catch (err) {
          console.error(`Error parsing allocate_ip response:`, err);
          allocateResult = {
            success: false,
            error: `Failed to parse response: ${err.message || 'Unknown error'}`
          };
        }

        if (allocateResult.success) {
          console.log(`Successfully allocated IP ${ipAddress} for ${deviceType} ${deviceId || 'new'}`);
          
          // Log additional success information
          if (deviceType === 'server' && allocateResult.server_updated) {
            console.log(`✅ Server IPMI updated in database for ${deviceType} ${deviceId}`);
          }
          
          if (deviceType === 'switch' && allocateResult.switch_updated) {
            console.log(`✅ Switch IP updated in inventory_switches table for switch ${deviceId}`);
          }
          
          console.log(`✅ IP marked as used (is_used = 1) in ip_addresses table: ${ipAddress}`);
          
          return true;
        } else {
          console.warn(`Regular allocation failed for IP ${ipAddress}: ${allocateResult.error || 'Unknown error'}`);
          console.log(`Attempting force allocation as fallback...`);

          // Try force allocation as fallback (can create IP if it doesn't exist)
          try {
            const forceAllocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: token,
                ip_address: ipAddress,
                manual_alocation: manual_alocation,
                notes: notes,
                for_server_ipmi: deviceType === 'server',
                for_switch: deviceType === 'switch',
                server_type: deviceType === 'server' ? (selectedTab === 'blade' ? 'blade' : 'dedicated') : undefined
              })
            });

            const forceAllocateResult = await forceAllocateResponse.json();

            if (forceAllocateResult.success) {
              console.log(`✅ Successfully force-allocated IP ${ipAddress} for ${deviceType} ${deviceId || 'new'}`);
              console.log(`✅ IP marked as used (is_used = 1) in ip_addresses table: ${ipAddress}`);
              return true;
            } else {
              console.error(`❌ Force allocation also failed for IP ${ipAddress}:`, forceAllocateResult.error || 'Unknown error');
              return false;
            }
          } catch (forceErr) {
            console.error(`❌ Error during force allocation for IP ${ipAddress}:`, forceErr);
            return false;
          }
        }
      } else {
        console.error(`Could not find IP ${ipAddress} in any subnet`);
        return false;
      }
    } catch (err) {
      console.error(`Error allocating IP ${ipAddress}:`, err);
      return false;
    }
  };

  const deallocateIp = async (ipAddress, deviceType, deviceId) => {
    if (!ipAddress) return;

    console.log(`Deallocating ${ipAddress} for ${deviceType} ${deviceId || 'new'}`);

    try {
      const token = localStorage.getItem('admin_token');

      // Call the force_deallocate_ip endpoint to deallocate the IP
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ip_address: ipAddress,
          for_server_ipmi: deviceType === 'server',
          for_switch: deviceType === 'switch',
          server_id: deviceType === 'server' ? deviceId : undefined,
          switch_id: deviceType === 'switch' ? deviceId : undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log(`Successfully deallocated IP ${ipAddress}`);
      } else {
        console.error(`Failed to deallocate IP ${ipAddress}:`, result.error || 'Unknown error');
      }
    } catch (err) {
      console.error(`Error deallocating IP ${ipAddress}:`, err);
    }
  };

  const handleEditItemChange = async (e) => {
    const { name, value } = e.target;
    const oldValue = selectedItem[name];

    // Handle IP address changes - deallocate old IP if changing or clearing
    if (oldValue && oldValue !== value && selectedItem.id) {
      if (name === 'ipmi') {
        // Handle IPMI address changes for servers
        console.log(`IPMI changed from ${oldValue} to ${value || 'empty'}, deallocating old IP`);
        await deallocateIp(oldValue, 'server', selectedItem.id);
      } else if (name === 'switch_ip') {
        // Handle switch IP address changes for switches
        console.log(`Switch IP changed from ${oldValue} to ${value || 'empty'}, deallocating old IP`);
        await deallocateIp(oldValue, 'switch', selectedItem.id);
      } else if (name === 'main_ip') {
        // Handle main server IP address changes for servers
        console.log(`Main IP changed from ${oldValue} to ${value || 'empty'}, deallocating old IP`);
        await deallocateIp(oldValue, 'server', selectedItem.id);
      } else if (name === 'additional_ips') {
        // Handle additional IPs changes for servers (comma-separated values)
        console.log(`Additional IPs changed from ${oldValue} to ${value || 'empty'}, deallocating old IPs`);
        
        // Get old and new IP lists
        const oldIps = oldValue ? oldValue.split(',').map(ip => ip.trim()).filter(ip => ip) : [];
        const newIps = value ? value.split(',').map(ip => ip.trim()).filter(ip => ip) : [];
        
        // Find IPs that were removed (in old but not in new)
        const removedIps = oldIps.filter(ip => !newIps.includes(ip));
        
        // Deallocate removed IPs
        for (const ip of removedIps) {
          console.log(`Deallocating removed additional IP: ${ip}`);
          await deallocateIp(ip, 'server', selectedItem.id);
        }
      }
    }

    // Create a copy of the selected item
    const updatedItem = { ...selectedItem };

    // Special handling for IPMI changes - clear related device detection fields
    if (name === 'ipmi' && oldValue !== value) {
      console.log('IPMI address changed, clearing device detection fields');
      // Clear device detection related fields that might be stale
      updatedItem.device_type = '';
      updatedItem.idrac_version = '';
      // Add any other device detection fields that should be cleared
    }

    // For numeric fields that should be numbers, convert string to number
    if (['size_ur', 'total_ports', 'rack_id', 'city_id', 'country_id'].includes(name) && value !== '') {
      updatedItem[name] = Number(value);
    } else {
      updatedItem[name] = value;
    }

    // Update the selected item state
    setSelectedItem(updatedItem);
  };


// Modify the loadAllData function (around line 600-650)
const loadAllData = async () => {
  setLoading(true);
  setError(null);

  try {
    switch(selectedTab) {
      case 'dedicated':
        await fetchDedicatedServers();
        break;
      case 'blade':
        await fetchBladeServers();
        // Ensure chassis data is available for location lookup in blade view
        await fetchChassis();
        break;
      case 'chassis':
        await fetchChassis();
        break;
      case 'switch':
        await fetchSwitches();
        await fetchSwitchModels(); // Add this line
        break;
    }

    // Load related data
    await Promise.all([
      fetchStorage(),
      fetchRacks(),
      fetchCities(),
      fetchCountries(),
      fetchCpuModels(),
      fetchRamConfigurations()
    ]);

    await fetchInventoryStats();

    setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
    setLoading(false);
  } catch (err) {
    console.error("Error loading data:", err);
    setError("Failed to load inventory data. Please try again.");
    setLoading(false);
  }
};

  // Effect to reload data when tab changes
  useEffect(() => {
    loadAllData();
  }, [selectedTab]);


  useEffect(() => {
// Replace the existing handleInventoryDataChange function with this improved version

// Enhanced event handler for inventory data changes with better port operation handling
const handleInventoryDataChange = async (event) => {
  console.log("Inventory data change event received:", event.detail);

  const { deviceType, deviceId, switchId, portNumber, timestamp, action, isLastPort } = event.detail;
  if (action === 'port-discovery' || action === 'ports-discovered') {
    console.log(`Port discovery action detected for switch ID: ${switchId}`);

    // Force refresh of the SwitchPortsManager component by updating its key
    setPortsRefreshKey(Date.now());

    // If this is for the currently selected switch, refresh it
    if (selectedTab === 'switch' && selectedItem && switchId && selectedItem.id.toString() === switchId.toString()) {
      try {
        // Force a refresh of the switch data
        const updatedSwitches = await fetchSwitches();
        const updatedSwitch = updatedSwitches.find(sw => sw.id.toString() === switchId.toString());

        if (updatedSwitch) {
          // Create a new object to ensure React detects the change
          const refreshedSwitch = { ...updatedSwitch, _forceRefresh: Date.now() };
          console.log("Updating selected switch after port discovery:", refreshedSwitch);
          setSelectedItem(refreshedSwitch);
        }

        // Force a UI refresh with a delay to ensure all updates are applied
        setTimeout(() => {
          console.log("Forcing UI refresh after port discovery");
          setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        }, 500);
      } catch (error) {
        console.error("Error refreshing switch after port discovery:", error);
      }
    }
  }

  // Flag to track if we need to update the selected item
  let shouldUpdateSelectedItem = false;

  // Handle port operations with special care
  const isPortOperation = action && (
    action.includes('port-') ||
    action === 'port-assignment' ||
    action === 'port-unassignment'
  );

  // Special handling for port unassignment when it's the last port
  if (action === 'port-unassignment' && isLastPort && switchId) {
    // If this was the last port, we need to update both server and switch data
    console.log("Last port was unassigned from device - refreshing switch and server data");

    // Refresh switch data to update port status
    if (switchId) {
      try {
        const updatedSwitches = await fetchSwitches();

        // If the switch is currently selected, update it
        if (selectedItem && selectedTab === 'switch' && selectedItem.id.toString() === switchId.toString()) {
          const updatedSwitch = updatedSwitches.find(sw => sw.id.toString() === switchId.toString());

          if (updatedSwitch) {
            console.log("Updating selected switch after port unassignment");
            setSelectedItem(updatedSwitch);
            shouldUpdateSelectedItem = true;
          }
        }
      } catch (error) {
        console.error("Error refreshing switches after port operation:", error);
      }
    }
  }

  // Regular refresh based on device type
  if (deviceType === 'dedicated') {
    try {
      const updatedServers = await fetchDedicatedServers();

      // If the currently selected item was modified, update it
      if (selectedItem && deviceId && selectedItem.id.toString() === deviceId.toString()) {
        const updatedServer = updatedServers.find(server => server.id.toString() === deviceId.toString());

        if (updatedServer) {
          console.log("Updating selected dedicated server after port change");
          setSelectedItem(updatedServer);
          shouldUpdateSelectedItem = true;
        }
      }
    } catch (error) {
      console.error("Error refreshing dedicated servers after port operation:", error);
    }
  } else if (deviceType === 'blade') {
    try {
      const updatedServers = await fetchBladeServers();

      // If the currently selected item was modified, update it
      if (selectedItem && deviceId && selectedItem.id.toString() === deviceId.toString()) {
        const updatedServer = updatedServers.find(server => server.id.toString() === deviceId.toString());

        if (updatedServer) {
          console.log("Updating selected blade server after port change");
          setSelectedItem(updatedServer);
          shouldUpdateSelectedItem = true;
        }
      }
    } catch (error) {
      console.error("Error refreshing blade servers after port operation:", error);
    }
  } else if (deviceType === 'switch') {
    try {
      const updatedSwitches = await fetchSwitches();

      // If the currently selected item was modified, update it
      if (selectedItem && deviceId && selectedItem.id.toString() === deviceId.toString()) {
        const updatedSwitch = updatedSwitches.find(sw => sw.id.toString() === deviceId.toString());

        if (updatedSwitch) {
          console.log("Updating selected switch after change");
          setSelectedItem(updatedSwitch);
          shouldUpdateSelectedItem = true;
        }
      }
    } catch (error) {
      console.error("Error refreshing switches after port operation:", error);
    }
  } else if (deviceType === 'all') {
    // Refresh all data types
    try {
      await loadAllData();
      shouldUpdateSelectedItem = true;
    } catch (error) {
      console.error("Error refreshing all data:", error);
    }
  }

  // If we're handling a port operation and the selected item was modified,
  // perform an extra refresh of the specific item to ensure UI consistency
  if (isPortOperation && shouldUpdateSelectedItem && selectedItem) {
    console.log("Port operation detected - performing additional refresh for UI consistency");

    try {
      // Depending on the currently selected device type, get a fresh copy of the data
      let refreshEndpoint = '';
      let refreshParam = '';
      let itemId = selectedItem.id;

      switch(selectedTab) {
        case 'dedicated':
          refreshEndpoint = 'get_inventory_dedicated_single';
          refreshParam = 'server_id';
          break;
        case 'blade':
          refreshEndpoint = 'get_blade_server_inventory_single';
          refreshParam = 'server_id';
          break;
        case 'switch':
          refreshEndpoint = 'get_inventory_switch_single';
          refreshParam = 'switch_id';
          break;
      }

      if (refreshEndpoint && itemId) {
        const token = localStorage.getItem('admin_token');

        // Try to get the single item directly
        const refreshResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=${refreshEndpoint}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: token,
            [refreshParam]: itemId
          })
        });

        if (refreshResponse.ok) {
          const freshItemData = await refreshResponse.json();
          console.log("Fresh item data fetched for UI consistency:", freshItemData);

          // Update the selected item with fresh data
          if (freshItemData) {
            setSelectedItem(freshItemData);

            // Force a UI refresh after updating the selected item
            setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
          }
        }
      }
    } catch (refreshError) {
      console.error("Error performing additional item refresh:", refreshError);
    }
  }

  // Force UI refresh after port operations to ensure consistency
  if (isPortOperation) {
    // Add a slightly longer delay to ensure backend processing completes
    // and all state updates have been applied
    setTimeout(() => {
      console.log("Forcing refresh of inventory data after port operation");
      // Update last updated timestamp to trigger re-render
      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
    }, 800);
  }
};

    // Add event listener
    window.addEventListener('inventory-data-change', handleInventoryDataChange);

    // Clean up
    return () => {
      window.removeEventListener('inventory-data-change', handleInventoryDataChange);
    };
  }, [selectedItem, dedicatedServers, bladeServers]);

// RAM Field Component (similar to the Password Field component)
const RamField = ({
  isEditMode,
  value,
  onChange,
  ramConfigurations,
  onAddClick,
  displayValue,
  label = "RAM"
}) => {
  return (
    <div>
      <div className="text-xs text-gray-500">{label}</div>
      {isEditMode ? (
        <div className="flex">
          <select
            name="ram"
            value={value || ''}
            onChange={onChange}
            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">Select RAM Configuration</option>
            {ramConfigurations.map(ram => (
              <option key={ram.id} value={ram.id}>{ram.description}</option>
            ))}
          </select>
          <button
            type="button"
            onClick={onAddClick}
            className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
            title="Add New RAM Configuration"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      ) : (
        <div className="font-medium flex items-center">
          <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
          {displayValue || 'Not specified'}
        </div>
      )}
    </div>
  );
};


// Add near other field components (around line 400-500)
// Switch Model Field Component
const SwitchModelField = ({
  isEditMode,
  value,
  onChange,
  switchModels,
  onAddClick,
  label = "Switch Model"
}) => {
  return (
    <div>
      <div className="text-xs text-gray-500">{label}</div>
      {isEditMode ? (
        <div className="flex">
          <select
            name="model_id"
            value={value || ''}
            onChange={onChange}
            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">Select Switch Model</option>
            {switchModels.map(model => (
              <option key={model.id} value={model.id}>{model.name} ({model.size}U)</option>
            ))}
          </select>
          <button
            type="button"
            onClick={onAddClick}
            className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
            title="Add New Switch Model"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      ) : (
        <div className="font-medium flex items-center">
          <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
          {value || 'Not specified'}
        </div>
      )}
    </div>
  );
};

// CPU Field Component
const CpuField = ({
  isEditMode,
  value,
  onChange,
  cpuModels,
  onAddClick,
  label = "CPU"
}) => {
  return (
    <div>
      <div className="text-xs text-gray-500">{label}</div>
      {isEditMode ? (
        <div className="flex">
          <select
            name="cpu"
            value={value || ''}
            onChange={onChange}
            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">Select CPU Model</option>
            {cpuModels.map(cpu => (
              <option key={cpu.id} value={cpu.cpu}>{cpu.cpu}</option>
            ))}
          </select>
          <button
            type="button"
            onClick={onAddClick}
            className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
            title="Add New CPU Model"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      ) : (
        <div className="font-medium flex items-center">
          <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
          {value || 'Not specified'}
        </div>
      )}
    </div>
  );
};

  // Fetch dedicated servers
  const fetchDedicatedServers = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_dedicated`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response text:', errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        // Ensure minimum required fields
        const processedServers = data.map(server => ({
          ...server,
          city_name: server.city_name || 'Unknown',
          country_name: server.country_name || 'Unknown',
          datacenter: server.datacenter || 'Unknown',
          // Ensure client info is accessible even if null
          client_name: server.client_name || 'Unknown Client',
          company_name: server.company_name || null
        }));

        setDedicatedServers(processedServers);

        // Extract unique locations
        const locations = new Set(['All', ...processedServers.map(server =>
          `${server.city_name}, ${server.country_name}`
        )]);
        setUniqueLocations(Array.from(locations));
      } else {
        console.warn("Received non-array data");
      }
    } catch (err) {
      console.error("Error fetching dedicated servers:", err);
      setError(`Failed to load servers: ${err.message}`);
      throw err;
    }
  };

  // Fetch chassis
  const fetchChassis = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_chassis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setChassis(data);
      }
    } catch (err) {
      console.error("Error fetching chassis:", err);
      throw err;
    }
  };

  // Fetch switches
  const fetchSwitches = async () => {
    console.log('Fetching switches from API...');

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switches`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // Check if data is valid
      if (!Array.isArray(data)) {
        console.error("API returned non-array data for switches:", data);
        throw new Error("Invalid data format returned from API");
      }

      console.log(`Successfully fetched ${data.length} switches from API`);

      // Update state with the new data
      setSwitches(data);

      return data; // Return data for any additional processing
    } catch (err) {
      console.error("Error fetching switches:", err);
      throw err; // Re-throw to allow caller to handle
    }
  };

  // Fetch storage data
  const fetchStorage = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_storage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setStorage(data);
      }
    } catch (err) {
      console.error("Error fetching storage:", err);
      setStorage([]);
    }
  };

  // Fetch racks
  const fetchRacks = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_racks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setRacks(data);
      }
    } catch (err) {
      console.error("Error fetching racks:", err);
      setRacks([]);
    }
  };

  // Fetch cities
  const fetchCities = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCities(data);
      }
    } catch (err) {
      console.error("Error fetching cities:", err);
    }
  };

  // Fetch countries
  const fetchCountries = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_countries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCountries(data);
      }
    } catch (err) {
      console.error("Error fetching countries:", err);
    }
  };

  const getDatacenterName = (cityId) => {
    if (!cityId || cities.length === 0) return '';

    const cityInfo = cities.find(city => city.id === parseInt(cityId));
    return cityInfo?.datacenter || '';
  };

  // Fetch inventory statistics
  const fetchInventoryStats = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data) {
        setInventoryStats(data);
      }
    } catch (err) {
      console.error("Error fetching inventory stats:", err);
    }
  };

  // Handle search input change
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle status filter change
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
  };

  // Handle location filter change
  const handleLocationFilter = (location) => {
    setSelectedLocation(location);
  };

  // Handle CPU filter change
  const handleCpuFilter = (cpuModel) => {
    setSelectedCpuModel(cpuModel);
  };

  // Handle sorting by field
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Fetch blade servers
  const fetchBladeServers = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_blade_server_inventory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setBladeServers(data);

        // Extract unique statuses if available
        const statuses = new Set(['All']);
        data.forEach(server => {
          if (server.status) {
            statuses.add(server.status);
          }
        });

        // Update the unique statuses state if we found any
        if (statuses.size > 1) {
          setUniqueStatuses(Array.from(statuses));
        }

        console.log(`Loaded ${data.length} blade servers from inventory`);
      } else {
        // If API returns empty or invalid data
        console.log("No blade servers found or invalid data format");
        setBladeServers([]);
      }
    } catch (err) {
      console.error("Error fetching blade servers:", err);
      setBladeServers([]);
      throw err;
    }
  };



  // Component for IPMI Access
  const IpmiAccessField = ({ ipmiAddress, rootPassword, isEditMode }) => {
    const [copyStatus, setCopyStatus] = useState('');

    const handleIpmiClick = () => {
      if (!ipmiAddress) return;

      // Copy root password to clipboard if available
      if (rootPassword) {
        navigator.clipboard.writeText(rootPassword)
          .then(() => {
            setCopyStatus('copied');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          })
          .catch(err => {
            console.error('Failed to copy password: ', err);
            setCopyStatus('error');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          });
      }

      // Open IPMI in new tab
      const ipmiUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress
        : `http://${ipmiAddress}`;
      window.open(ipmiUrl, '_blank');
    };

    if (isEditMode) {
      return null; // Don't render in edit mode
    }

    return (
      <div className="mt-2">
        {ipmiAddress && (
          <button
            onClick={handleIpmiClick}
            className="flex items-center px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded text-xs font-medium transition-colors"
          >
            <Eye className="w-3 h-3 mr-1" />
            Open IPMI Interface
            {rootPassword && (
              <span className="ml-1">
                {copyStatus === 'copied' ? (
                  <span className="text-green-600 ml-1">(Password copied!)</span>
                ) : copyStatus === 'error' ? (
                  <span className="text-red-600 ml-1">(Copy failed)</span>
                ) : (
                  <span className="text-gray-500 ml-1">(will copy root password)</span>
                )}
              </span>
            )}
          </button>
        )}
      </div>
    );
  };

  // Component for launching iDRAC Console
  const IdracConsoleLauncher = ({ ipmiAddress, username, password, version }) => {
    const [launching, setLaunching] = useState(false);
    const [showInstructions, setShowInstructions] = useState(false);

    // Generate JNLP file content for iDRAC
    const generateJnlpContent = () => {
      // Base URL for iDRAC
      const baseUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress.replace(/^https?:\/\//, '')
        : ipmiAddress;

      // Different JNLP configuration based on iDRAC version
      if (version === 9) {
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>Virtual Console Client for iDRAC9</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>IP=${baseUrl}</argument>
    <argument>JNLPSTR=JViewer</argument>
    <argument>JNLPNAME=JViewer.jnlp</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>apcp=1</argument>
    <argument>version=2</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.8+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      } else {
        // Default to iDRAC 8 format
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>iDRAC8 Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>iDRAC8 Virtual Console Client</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>ip=${baseUrl}</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>title=iDRAC8 Virtual Console Client</argument>
    <argument>helpurl=https://${baseUrl}:443/help/contents.html</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.6.0_24+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      }
    };

    // Download JNLP file function
    const downloadJnlpFile = () => {
      setLaunching(true);

      try {
        const jnlpContent = generateJnlpContent();
        const blob = new Blob([jnlpContent], { type: 'application/x-java-jnlp-file' });
        const url = URL.createObjectURL(blob);

        // Create temporary link element to download the file
        const link = document.createElement('a');
        link.href = url;
        link.download = `idrac${version}-console.jnlp`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
          URL.revokeObjectURL(url);
          setLaunching(false);
        }, 1000);
      } catch (error) {
        console.error('Error generating JNLP file:', error);
        setLaunching(false);
      }
    };

    if (!ipmiAddress) return null;

    return (
      <div className="mt-1">
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={downloadJnlpFile}
            disabled={launching}
            className={`flex items-center px-2 py-1 text-xs font-medium rounded transition-colors
              ${launching ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'}`}
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17L15 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 6V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L9 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
            {launching ? 'Generating...' : `Launch Java Console`}
          </button>

          <button
            onClick={() => setShowInstructions(!showInstructions)}
            className="flex items-center px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs transition-colors"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 7V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="12" cy="17" r="1" fill="currentColor"/>
            </svg>
            {showInstructions ? 'Hide Help' : 'Help'}
          </button>
        </div>

        {showInstructions && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs">
            <h4 className="font-medium mb-1">How to use iDRAC {version} Java Console:</h4>
            <ol className="list-decimal pl-4 space-y-1">
              <li>Download and install Java if you haven't already
                (<a href="https://www.java.com/download/" target="_blank" rel="noopener noreferrer"
                   className="text-blue-600 hover:underline">www.java.com</a>)</li>
              <li>Save the .jnlp file when prompted</li>
              <li>Right-click the downloaded file and select "Open with" → "Java Web Start"</li>
              <li>If prompted with security warnings, click "Run" to continue</li>
              <li>The iDRAC console should load with your credentials auto-filled</li>
            </ol>
            <p className="mt-2 text-gray-600">Note: You may need to add the iDRAC IP to the Java security exception site list in your Java Control Panel.</p>
          </div>
        )}
      </div>
    );
  };
  // Update blade server
  const handleUpdateBladeServer = async () => {
    try {
      // Validate required fields
      if (!editItem.id || !editItem.label) {
        alert('Server ID and label are required');
        return;
      }

      const token = localStorage.getItem('admin_token');

      // Make the API request
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=update_blade_server_inventory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ...editItem
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Close modal and refresh data
        handleCloseEditItemModal();

        // Refresh the blade servers list
        await fetchBladeServers();

        // If the selected item is the one being edited, update it
        if (selectedItem && selectedItem.id === editItem.id) {
          setSelectedItem({...editItem});
        }
      } else {
        alert(result.error || 'Failed to update blade server');
      }
    } catch (err) {
      console.error("Error updating blade server:", err);
      alert('Failed to update blade server: ' + err.message);
    }
  };

  const handleDeleteBladeServer = async (id) => {
    // Convert id to number if it's a string
    id = typeof id === 'string' ? parseInt(id) : id;

    console.log("handleDeleteBladeServer called with ID:", id, "Type:", typeof id);

    if (!window.confirm('Are you sure you want to delete this blade server?')) {
      return;
    }

    try {
      setLoading(true);

      // Find the server to be deleted for logging released resources
      const bladeToDelete = bladeServers.find(blade =>
        parseInt(blade.id) === id || blade.id === id
      );

      console.log(`Attempting to delete blade server:`, bladeToDelete);

      const token = localStorage.getItem('admin_token');

      // Create the request body, explicitly ensuring id is numeric
      const requestBody = {
        token: token,
        id: id
      };

      console.log("Request body:", JSON.stringify(requestBody));

      // Make the API request
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=delete_blade_server_inventory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      // Log raw response for debugging
      const responseText = await response.text();
      console.log("Raw API response:", responseText);

      // Parse the response
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse response as JSON:", parseError);
        throw new Error(`API response is not valid JSON: ${responseText}`);
      }

      if (result.success) {
        // Refresh the blade servers list
        await fetchBladeServers();

        // Close details if open
        if (selectedItem && (parseInt(selectedItem.id) === id || selectedItem.id === id)) {
          closeItemDetails();
        }

        // Build success message
        let successMessage = 'Blade server deleted successfully';

        // Include details about released resources if available
        if (result.ports_released && result.ports_released > 0) {
          successMessage += `\n${result.ports_released} switch port(s) have been released`;
        }

        if (result.chassis_updated) {
          successMessage += '\nChassis assignment has been removed';
        }

        if (bladeToDelete) {
          // Add subnet information if available
          if (bladeToDelete.main_ip) {
            successMessage += `\nMain subnet ${bladeToDelete.main_ip} has been released`;
          }
          if (bladeToDelete.additional_ips) {
            successMessage += `\nAdditional IPs have been released: ${bladeToDelete.additional_ips}`;
          }
        }

        alert(successMessage);
      } else {
        alert(result.error || 'Failed to delete blade server');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error deleting blade server:", err);
      alert('Failed to delete blade server: ' + err.message);
    }
  }


const handleBulkAddItems = async (items, deviceType) => {
  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');
    let endpoint = '';

    // Select the appropriate API endpoint based on device type
    switch(deviceType) {
      case 'blade':
        endpoint = 'add_blade_server_inventory';
        break;
      case 'dedicated':
        endpoint = 'add_dedicated_server';
        break;
      case 'chassis':
        endpoint = 'add_chassis';
        break;
      case 'switch':
        endpoint = 'add_switch';
        break;
      default:
        throw new Error('Invalid device type');
    }

    // Track results
    const results = {
      successful: [],
      failed: []
    };

    // Process items one by one
    for (const item of items) {
      try {
        // Extract switch and port information for later port assignment
        // This preserves the existing functionality but adds port assignment capability
        const switchInfo = {
          switchId: item.switch_id || null,
          switchName: item.switch || null,
          port1: item.port1 || null,
          port1_speed: item.port1_speed || null
        };

        // Create a copy of the item for the API
        let itemToSend = { ...item };

        // Special handling for switches
        if (deviceType === 'switch') {
          // Ensure rack_position is set (copy from position if needed)
          if (!itemToSend.rack_position && itemToSend.position) {
            itemToSend = { ...item, rack_position: item.position };
            console.log("Copying position to rack_position for switch item:", itemToSend);
          }
        }

        console.log(`Sending ${deviceType} item to API:`, itemToSend);

        const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token,
            ...itemToSend
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          const newId = result.id;

          // Process switch and port assignment for server types if needed
          if ((deviceType === 'dedicated' || deviceType === 'blade') &&
          switchInfo.switchId && switchInfo.port1) {
        try {
          // Add a delay to ensure the server is fully created
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Get the full list of ports for this switch to find the portId
          const ports = await fetchSwitchPorts(switchInfo.switchId);
          console.log(`Retrieved ${ports.length} ports for switch ${switchInfo.switchId}`);

          // Find the matching port by port_number
          const matchingPort = ports.find(port =>
            port.port_number.toString() === switchInfo.port1.toString()
          );

          if (matchingPort) {
            console.log(`Found matching port: ${JSON.stringify(matchingPort)}`);

            // For blade servers, we need to use the port ID, not the port number
            console.log(`Assigning port ${matchingPort.id} (${matchingPort.port_number}) to new ${deviceType} server ${newId}`);

            // Use the improved port assignment endpoint
            const response = await fetch(`${API_URL}/api_admin_inventory.php?f=assign_port_to_server_modal`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: token,
                port_id: matchingPort.id,
                device_id: newId,
                device_type: deviceType,
                port_field: 'port1' // Explicitly specify port1 field
              })
            });

            if (!response.ok) {
              const text = await response.text();
              console.error(`Port assignment API error: HTTP ${response.status}, Response: ${text}`);
              throw new Error(`Port assignment failed: ${response.status} ${text}`);
            }

            const result = await response.json();

            if (!result.success) {
              console.error(`Port assignment API error: ${result.error || 'Unknown error'}`);
            } else {
              console.log(`Port assignment successful: ${result.message}`);
            }
          } else {
            console.warn(`No matching port found for switch ${switchInfo.switchId}, port number ${switchInfo.port1}`);
          }
        } catch (portError) {
          console.error(`Error during port assignment:`, portError);
          // Don't fail the entire operation if port assignment fails
        }
      }

          // Save the successful result with the new ID
          results.successful.push({
            item: itemToSend,
            id: newId
          });

          // For switches, add to UI state immediately
          if (deviceType === 'switch') {
            // Create a complete switch object with related data
            const newSwitch = {
              id: newId,
              ...itemToSend,
              // Add additional fields for UI display
              city_name: cities.find(c => c.id === parseInt(itemToSend.city_id))?.city || 'Unknown',
              country_name: countries.find(c => c.id === parseInt(itemToSend.country_id))?.country || 'Unknown',
              rack_name: racks.find(r => r.id === parseInt(itemToSend.rack_id))?.rack_name || 'Unknown'
            };

            // Update switches state with the new item
            setSwitches(prevSwitches => [...prevSwitches, newSwitch]);
          }
        } else {
          results.failed.push({
            item: itemToSend,
            error: result.error || 'Unknown error'
          });
        }
      } catch (err) {
        results.failed.push({
          item,
          error: err.message
        });
      }
    }

    // Close the modal
    setBulkAddModalOpen(false);

    // After all items are processed, refresh the appropriate data
    try {
      console.log(`Refreshing ${deviceType} data after bulk add...`);
      if (deviceType === 'blade') {
        await fetchBladeServers();
      } else if (deviceType === 'dedicated') {
        await fetchDedicatedServers();
      } else if (deviceType === 'chassis') {
        await fetchChassis();
      } else if (deviceType === 'switch') {
        await fetchSwitches();
      }

      // Force UI refresh
      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);

      console.log(`${deviceType} data successfully refreshed`);
    } catch (fetchError) {
      console.error(`Error refreshing ${deviceType} data:`, fetchError);
      // Error is logged but we continue to show results
    }

    // Show summary
    if (results.successful.length > 0) {
      alert(`Successfully added ${results.successful.length} ${deviceType}(s)` +
            (results.failed.length > 0 ? `\nFailed to add ${results.failed.length} ${deviceType}(s)` : ''));
    } else if (results.failed.length > 0) {
      alert(`Failed to add any ${deviceType}s. Please check the console for details.`);
    }

    setLoading(false);
    return results;
  } catch (err) {
    setLoading(false);
    console.error(`Error adding ${deviceType}:`, err);
    alert(`Failed to add ${deviceType}s: ${err.message}`);
    throw err;
  }
};


  const assignPortToServer = async (portId, serverId, deviceType, serverLabel) => {
    try {
      const token = localStorage.getItem('admin_token');

      console.log(`Assigning port ${portId} to ${deviceType} server ${serverId} with label ${serverLabel}`);

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=assign_port_to_server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          port_id: portId,
          device_id: serverId,
          device_type: deviceType,
          server_label: serverLabel // Include server label
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Port assignment API error response:", errorText);
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to assign port');
      }

      // Emit an event to notify components that need to refresh
      const event = new CustomEvent('inventory-data-change', {
        detail: {
          timestamp: new Date().toISOString(),
          deviceType: deviceType,
          deviceId: serverId,
          action: 'port-assignment'
        }
      });
      window.dispatchEvent(event);

      return result;
    } catch (error) {
      console.error(`Error in assignPortToServer:`, error);
      throw error; // Re-throw to allow caller to handle
    }
  }

// Delete an inventory item
const handleDeleteItem = async (id) => {
  if (!window.confirm('Are you sure you want to delete this item?')) {
    return;
  }

  try {
    if (selectedTab === 'blade') {
      // Use the blade server specific handler
      await handleDeleteBladeServer(id);
      return;
    }

    const token = localStorage.getItem('admin_token');

    let endpoint = '';
    switch(selectedTab) {
      case 'dedicated':
        endpoint = 'delete_dedicated_server';
        break;
      case 'chassis':
        endpoint = 'delete_chassis';
        break;
      case 'switch':
        endpoint = 'delete_switch';
        break;
      default:
        throw new Error('Invalid item type');
    }

    // First get the item details to log what resources will be freed
    let itemToDelete = null;
    if (selectedTab === 'dedicated') {
      itemToDelete = dedicatedServers.find(server => server.id === id);
    } else if (selectedTab === 'switch') {
      itemToDelete = switches.find(sw => sw.id === id);
    } else if (selectedTab === 'chassis') {
      itemToDelete = chassis.find(ch => ch.id === id);
    }

    console.log(`Deleting ${selectedTab} with ID: ${id}`, itemToDelete);

    // Complete the API request implementation
    setLoading(true);

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        id: id
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      // Show resources that were freed
      let successMessage = `${selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1)} deleted successfully`;

      // Add details about released resources if available
      if (result.ports_released && result.ports_released > 0) {
        successMessage += `\n${result.ports_released} switch port(s) have been released`;
      }

      // Show subnet release if item had IP addresses assigned
      if (itemToDelete) {
        if (itemToDelete.main_ip) {
          successMessage += `\nMain subnet ${itemToDelete.main_ip} has been released`;
        }
        if (itemToDelete.additional_ips) {
          successMessage += `\nAdditional IPs have been released: ${itemToDelete.additional_ips}`;
        }
      }

      // Refresh the appropriate data based on tab
      switch(selectedTab) {
        case 'dedicated':
          await fetchDedicatedServers();
          break;
        case 'chassis':
          await fetchChassis();
          break;
        case 'switch':
          await fetchSwitches();
          break;
      }

      // Close details if open
      if (selectedItem && selectedItem.id === id) {
        closeItemDetails();
      }

      alert(successMessage);
    } else {
      // Special handling for chassis with associated blade servers
      if (selectedTab === 'chassis' && result.error && result.error.includes('blade servers associated')) {
        const forceDelete = window.confirm(`${result.error}\n\nWould you like to force delete this chassis? This will remove all blade server associations.`);

        if (forceDelete) {
          // Make a new request with force=true
          const forceResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token,
              id: id,
              force: true
            })
          });

          if (!forceResponse.ok) {
            throw new Error(`HTTP error ${forceResponse.status}`);
          }

          const forceResult = await forceResponse.json();

          if (forceResult.success) {
            // Refresh chassis data
            await fetchChassis();

            // Also refresh blade servers as their associations may have changed
            await fetchBladeServers();

            // Close details if open
            if (selectedItem && selectedItem.id === id) {
              closeItemDetails();
            }

            alert('Chassis force deleted successfully');
          } else {
            alert(forceResult.error || 'Failed to force delete chassis');
          }
        }
      } else {
        alert(result.error || `Failed to delete ${selectedTab}`);
      }
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error(`Error deleting ${selectedTab}:`, err);
    alert(`Failed to delete ${selectedTab}: ${err.message}`);
  }
}

  // Get the sort icon based on current sort status
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Handle refresh data click
  const handleRefreshData = async () => {
    await loadAllData();
  };

  // Handle clicking on an inventory item
  const handleItemClick = async (item) => {
    setSelectedItem(item);

    // If viewing a chassis and blade servers haven't been loaded yet
    if (selectedTab === 'chassis' && bladeServers.length === 0) {
      try {
        await fetchBladeServers();
      } catch (err) {
        console.error("Error loading blade servers for chassis:", err);
      }
    }
  };

  // Close item details modal
  const closeItemDetails = () => {
    setSelectedItem(null);
  };

  const fetchReferenceData = async () => {
    try {
      // Fetch all reference data needed for forms in parallel
      await Promise.all([
        switches.length === 0 ? fetchSwitches() : Promise.resolve(),
        chassis.length === 0 ? fetchChassis() : Promise.resolve(),
        racks.length === 0 ? fetchRacks() : Promise.resolve(),
        cities.length === 0 ? fetchCities() : Promise.resolve(),
        countries.length === 0 ? fetchCountries() : Promise.resolve(),
        // Add this line to fetch switch models when needed
        selectedTab === 'switch' ? fetchSwitchModels() : Promise.resolve()
      ]);
    } catch (err) {
      console.error("Error loading reference data:", err);
      setError("Failed to load reference data. Please try again.");
    }
  };

  const handleOpenAddItemModal = async (bulk = false) => {
    setLoading(true);

    // Ensure reference data is loaded
    await fetchReferenceData();

    setLoading(false);

    if (bulk) {
      setBulkAddModalOpen(true);
    } else {
      setAddItemModalOpen(true);
    }
  };

// Update item handler
const handleUpdateItem = async () => {
  try {
    if (!selectedItem || !selectedItem.id) {
      alert('Invalid item data');
      return;
    }

    console.log("Selected item before API call:", JSON.stringify(selectedItem, null, 2));

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    let endpoint = '';
    switch(selectedTab) {
      case 'dedicated':
        endpoint = 'update_dedicated_server';
        break;
      case 'blade':
        endpoint = 'update_blade_server_inventory';
        break;
      case 'chassis':
        endpoint = 'update_chassis';
        break;
      case 'switch':
        endpoint = 'update_switch';
        break;
      default:
        throw new Error('Invalid item type');
    }

    const apiData = {
      token: token,
      ...selectedItem
    };

    console.log(`Sending to ${endpoint}:`, JSON.stringify(apiData, null, 2));

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(apiData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      // Handle IP allocation after successful update
      let ipAllocationWarnings = [];

      // Handle IPMI IP allocation for servers (only if not empty)
      if ((selectedTab === 'dedicated' || selectedTab === 'blade') && selectedItem.ipmi && selectedItem.ipmi.trim()) {
        console.log(`Allocating IPMI IP ${selectedItem.ipmi} for ${selectedTab} server ${selectedItem.id} (${selectedItem.label})`);
        const ipAllocated = await allocateIp(selectedItem.ipmi, 'server', selectedItem.id, selectedItem.label || 'Unknown');
        if (!ipAllocated) {
          console.warn(`Failed to allocate IPMI IP ${selectedItem.ipmi}, but server update was successful`);
          ipAllocationWarnings.push(`IPMI IP ${selectedItem.ipmi}`);
        } else {
          console.log(`Successfully allocated IPMI IP ${selectedItem.ipmi} for server ${selectedItem.id}`);
        }
      }

      // Handle main IP allocation for servers (only if not empty)
      if ((selectedTab === 'dedicated' || selectedTab === 'blade') && selectedItem.main_ip && selectedItem.main_ip.trim()) {
        console.log(`Allocating main IP ${selectedItem.main_ip} for ${selectedTab} server ${selectedItem.id} (${selectedItem.label})`);
        const ipAllocated = await allocateIp(selectedItem.main_ip, 'server', selectedItem.id, selectedItem.label || 'Unknown');
        if (!ipAllocated) {
          console.warn(`Failed to allocate main IP ${selectedItem.main_ip}, but server update was successful`);
          ipAllocationWarnings.push(`Main IP ${selectedItem.main_ip}`);
        } else {
          console.log(`Successfully allocated main IP ${selectedItem.main_ip} for server ${selectedItem.id}`);
        }
      }

      // Handle additional IPs allocation for servers (only if not empty)
      if ((selectedTab === 'dedicated' || selectedTab === 'blade') && selectedItem.additional_ips && selectedItem.additional_ips.trim()) {
        const additionalIps = selectedItem.additional_ips.split(',').map(ip => ip.trim()).filter(ip => ip);
        for (const ip of additionalIps) {
          console.log(`Allocating additional IP ${ip} for ${selectedTab} server ${selectedItem.id} (${selectedItem.label})`);
          const ipAllocated = await allocateIp(ip, 'server', selectedItem.id, selectedItem.label || 'Unknown');
          if (!ipAllocated) {
            console.warn(`Failed to allocate additional IP ${ip}, but server update was successful`);
            ipAllocationWarnings.push(`Additional IP ${ip}`);
          } else {
            console.log(`Successfully allocated additional IP ${ip} for server ${selectedItem.id}`);
          }
        }
      }

      // Handle switch IP allocation for switches (only if not empty)
      if (selectedTab === 'switch' && selectedItem.switch_ip && selectedItem.switch_ip.trim()) {
        console.log(`Allocating switch IP ${selectedItem.switch_ip} for switch ${selectedItem.id} (${selectedItem.label})`);
        const ipAllocated = await allocateIp(selectedItem.switch_ip, 'switch', selectedItem.id, selectedItem.label || 'Unknown');
        if (!ipAllocated) {
          console.warn(`Failed to allocate switch IP ${selectedItem.switch_ip}, but switch update was successful`);
          ipAllocationWarnings.push(`Switch IP ${selectedItem.switch_ip}`);
        } else {
          console.log(`Successfully allocated switch IP ${selectedItem.switch_ip} for switch ${selectedItem.id}`);
        }
      }

      // Show appropriate success/warning message
      if (ipAllocationWarnings.length > 0) {
        showToast(`${selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1)} updated successfully, but failed to allocate: ${ipAllocationWarnings.join(', ')}`, 'warning');
      } else {
        showToast(`${selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1)} updated successfully!`, 'success');
      }

      // Turn off edit mode
      setIsEditMode(false);

      // Get fresh data directly from the API for the specific item
      // This is more reliable than the list fetches which might have caching issues
      let refreshEndpoint = '';
      let refreshParam = '';

      switch(selectedTab) {
        case 'dedicated':
          refreshEndpoint = 'get_inventory_dedicated_single';
          refreshParam = 'server_id';
          break;
        case 'blade':
          refreshEndpoint = 'get_blade_server_inventory_single';
          refreshParam = 'server_id';
          break;
        case 'chassis':
          refreshEndpoint = 'get_inventory_chassis_single';
          refreshParam = 'chassis_id';
          break;
        case 'switch':
          refreshEndpoint = 'get_inventory_switch_single';
          refreshParam = 'switch_id';
          break;
      }

      // If the specific endpoint doesn't exist, fall back to getting all data
      // and filtering for our item - this ensures compatibility
      let freshItemData;

      try {
        // First try to get single item directly
        const refreshResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=${refreshEndpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token,
            [refreshParam]: selectedItem.id
          })
        });

        if (refreshResponse.ok) {
          freshItemData = await refreshResponse.json();
          console.log("Fresh item data fetched directly:", freshItemData);
        } else {
          throw new Error("Single item endpoint not available");
        }
      } catch (refreshError) {
        console.log("Could not fetch single item, fetching full list instead:", refreshError);

        // If direct fetch fails, get the full list and filter
        let listEndpoint = '';

        switch(selectedTab) {
          case 'dedicated':
            listEndpoint = 'get_inventory_dedicated';
            break;
          case 'blade':
            listEndpoint = 'get_blade_server_inventory';
            break;
          case 'chassis':
            listEndpoint = 'get_inventory_chassis';
            break;
          case 'switch':
            listEndpoint = 'get_inventory_switches';
            break;
        }

        const listResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=${listEndpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        if (!listResponse.ok) {
          throw new Error(`HTTP error ${listResponse.status}`);
        }

        const listData = await listResponse.json();
        freshItemData = listData.find(item => item.id.toString() === selectedItem.id.toString());
        console.log("Fresh item data fetched from list:", freshItemData);
      }

      if (freshItemData) {
        // Directly update the selectedItem with fresh data from the API
        setSelectedItem(freshItemData);

        // Also update the corresponding item in the appropriate list
        switch(selectedTab) {
          case 'dedicated':
            setDedicatedServers(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? freshItemData : item)
            );
            break;
          case 'blade':
            setBladeServers(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? freshItemData : item)
            );
            break;
          case 'chassis':
            setChassis(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? freshItemData : item)
            );
            break;
          case 'switch':
            setSwitches(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? freshItemData : item)
            );
            break;
        }
      } else {
        console.error("Could not find updated item in response");
        // If we can't fetch the fresh item data, still update the UI with what we have
        // This ensures at least the edited fields are shown correctly

        // Update the corresponding list
        switch(selectedTab) {
          case 'dedicated':
            setDedicatedServers(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? selectedItem : item)
            );
            break;
          case 'blade':
            setBladeServers(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? selectedItem : item)
            );
            break;
          case 'chassis':
            setChassis(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? selectedItem : item)
            );
            break;
          case 'switch':
            setSwitches(prevList =>
              prevList.map(item => item.id.toString() === selectedItem.id.toString()
                ? selectedItem : item)
            );
            break;
        }
      }

      // Also refresh all data in the background to ensure everything is up to date
      switch(selectedTab) {
        case 'dedicated':
          fetchDedicatedServers();
          break;
        case 'blade':
          fetchBladeServers();
          break;
        case 'chassis':
          fetchChassis();
          break;
        case 'switch':
          fetchSwitches();
          break;
      }

      // Update timestamp
      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);

      // Show success message
      alert('Changes saved successfully!');
    } else {
      alert(result.error || `Failed to update ${selectedTab}`);
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error(`Error updating ${selectedTab}:`, err);
    alert(`Failed to update ${selectedTab}: ${err.message}`);
  }
};

  // Handle closing the add item modal
  const handleCloseAddItemModal = () => {
    setAddItemModalOpen(false);
    // Reset new item form
    setNewItem({
      label: '',
      cpu: '',
      switch: '',
      port1: '',
      port1_speed: '',
      port2: '',
      port2_speed: '',
      port3: '',
      port3_speed: '',
      port4: '',
      port4_speed: '',
      bay1: '',
      bay2: '',
      bay3: '',
      bay4: '',
      bay5: '',
      bay6: '',
      bay7: '',
      bay8: '',
      bay9: '',
      bay10: '',
      mac: '',
      ipmi: '',
      rootUser: '',
      notes: '',
      main_ip: '',
      additional_ips: ''
    });
  };

  // Handle opening the edit item modal
  const handleOpenEditItemModal = (item) => {
    setEditItem({...item});
    setEditItemModalOpen(true);
    setIsEditMode(true);
  };

  // Handle closing the edit item modal
  const handleCloseEditItemModal = () => {
    setEditItemModalOpen(false);
    setEditItem(null);
    setIsEditMode(false);
  };

  // Handle changes in the new item form
  const handleNewItemChange = (e) => {
    const { name, value } = e.target;
    setNewItem(prevItem => ({
      ...prevItem,
      [name]: value
    }));
  };

  // Component for iDRAC auto-detection
  const IdracAutoDetectConsole = ({ ipmiAddress, username, password }) => {
    const [idracVersion, setIdracVersion] = useState(null);
    const [detecting, setDetecting] = useState(true);

    useEffect(() => {
      // Auto-detect iDRAC version when component mounts
      if (ipmiAddress) {
        setDetecting(true);

        // Try to extract version from label if available
        const detectedFromLabel = selectedItem?.label?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromLabel && (detectedFromLabel[1] === '8' || detectedFromLabel[1] === '9')) {
          setIdracVersion(parseInt(detectedFromLabel[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from notes
        const detectedFromNotes = selectedItem?.notes?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromNotes && (detectedFromNotes[1] === '8' || detectedFromNotes[1] === '9')) {
          setIdracVersion(parseInt(detectedFromNotes[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from model information (Dell servers with specific generations)
        const modelInfo = [
          { pattern: /r[0-9]40/i, version: 9 },    // R740, R640, R440, etc. = iDRAC 9
          { pattern: /r[0-9]30/i, version: 8 },    // R730, R630, R430, etc. = iDRAC 8
          { pattern: /r[0-9]20/i, version: 7 },    // R720, R620, R520, etc. = iDRAC 7
          { pattern: /poweredge\s*1[4-9]/i, version: 9 },  // PowerEdge 14G+ = iDRAC 9
          { pattern: /poweredge\s*1[23]/i, version: 8 },   // PowerEdge 12G-13G = iDRAC 8
          { pattern: /g1[4-9]/i, version: 9 },     // Dell G14+ servers = iDRAC 9
          { pattern: /g1[23]/i, version: 8 },      // Dell G12-G13 servers = iDRAC 8
        ];

        // Check against server label and notes
        const serverInfo = (selectedItem?.label || '') + ' ' + (selectedItem?.cpu || '') + ' ' + (selectedItem?.notes || '');

        for (const model of modelInfo) {
          if (model.pattern.test(serverInfo)) {
            setIdracVersion(model.version);
            setDetecting(false);
            return;
          }
        }

        // Default to iDRAC 8 if no detection method succeeds
        setIdracVersion(8);
        setDetecting(false);
      }
    }, [ipmiAddress]);

    if (!ipmiAddress) return null;

    // Only show for iDRAC 8 or 9 (iDRAC 7 and earlier used different mechanisms)
    if (idracVersion !== 8 && idracVersion !== 9) return null;

    return (
      <div className="mt-2 border-t pt-2">
        <div className="text-xs text-gray-500 mb-1">Launch iDRAC Console</div>

        {detecting ? (
          <div className="flex items-center text-xs text-gray-500">
            <RefreshCw className="w-3 h-3 animate-spin mr-1" />
            Detecting iDRAC version...
          </div>
        ) : (
          <IdracConsoleLauncher
            ipmiAddress={ipmiAddress}
            username={username}
            password={password}
            version={idracVersion}
          />
        )}
      </div>
    );
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Active': 'bg-green-100 text-green-800', // Keep for backward compatibility
      'In use': 'bg-yellow-100 text-yellow-800',
      'Defect': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />, // Keep for backward compatibility
      'In use': <Activity className="w-4 h-4 mr-1" />,
      'Defect': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // ----------------------
  // Storage helper functions
  // ----------------------
  const calculateTotalStorage = (item) => {
    let total = 0;
    // Iterate through possible bay fields (covers both dedicated & blade layouts)
    for (let i = 1; i <= 26; i++) {
      const bayVal = item[`bay${i}`];
      if (bayVal && bayVal !== '0') {
        const cfg = storageConfigurations.find((c) => String(c.id) === String(bayVal));
        if (cfg) {
          total += cfg.size; // size is stored in GB
        }
      }
    }
    return total; // in GB
  };

  const formatStorage = (capacityGb) => {
    if (!capacityGb || capacityGb === 0) return '0GB';
    if (capacityGb >= 1000) {
      const tbVal = capacityGb / 1000;
      return `${Number.isInteger(tbVal) ? tbVal : tbVal.toFixed(1)}TB`;
    }
    return `${capacityGb}GB`;
  };

  // Show disk modal on hover
  const showStorageModal = (event, item) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const disks = [];
    for (let i = 1; i <= 26; i++) {
      const bayVal = item[`bay${i}`];
      if (bayVal && bayVal !== '0') {
        const cfg = storageConfigurations.find((c) => String(c.id) === String(bayVal));
        if (cfg) {
          disks.push({ bay: i, label: cfg.label });
        }
      }
    }
    if (disks.length === 0) return;
    setStorageModalData({
      disks,
      x: rect.left + rect.width / 2,
      y: rect.bottom + window.scrollY + 4,
    });
  };

  const hideStorageModal = () => {
    setStorageModalData(null);
  };

  // Filter inventory items based on search query and filters
  const getFilteredItems = () => {
    let items = [];

    switch(selectedTab) {
      case 'dedicated':
        items = dedicatedServers;
        break;
      case 'blade':
        items = bladeServers;
        break;
      case 'chassis':
        items = chassis;
        break;
      case 'switch':
        items = switches;
        break;
    }

    return items.filter(item => {
      const matchesSearch =
        (item.label && item.label.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.notes && item.notes.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.id && item.id.toString().includes(searchQuery));

      const matchesStatus = selectedStatus === 'All' || item.status === selectedStatus;

      // Enhanced location matching
      let matchesLocation = true;
      if (selectedLocation !== 'All') {
        const [selCityRaw, selCountryRaw] = selectedLocation.split(',');
        const selCity = selCityRaw.trim().toLowerCase();
        const selCountry = selCountryRaw ? selCountryRaw.trim().toLowerCase() : 'unknown';

        const itemCity = (item.city_name || '').trim().toLowerCase();
        const itemCountry = (item.country_name || 'Unknown').trim().toLowerCase();

        // City must match exactly
        if (itemCity !== selCity) {
          matchesLocation = false;
        } else {
          // Country must match unless one of them is unknown
          if (selCountry !== 'unknown' && itemCountry !== 'unknown' && itemCountry !== selCountry) {
            matchesLocation = false;
          }
        }
      }

      const cpuNameForFilter = (item.cpu_name || item.cpu || 'Unknown');
      const matchesCpu = selectedCpuModel === 'All' || cpuNameForFilter === selectedCpuModel;

      return matchesSearch && matchesStatus && matchesLocation && matchesCpu;
    });
  };

  const getSortedItems = () => {
    const filteredItems = getFilteredItems();

    return [...filteredItems].sort((a, b) => {
      let comparison = 0;
      if (sortField === 'label') {
        comparison = (a.label || '').localeCompare(b.label || '');
      } else if (sortField === 'location') {
        const locA = a.city_name ? `${a.city_name}, ${a.country_name}` : '';
        const locB = b.city_name ? `${b.city_name}, ${b.country_name}` : '';
        comparison = locA.localeCompare(locB);
      } else if (sortField === 'status') {
        comparison = (a.status || '').localeCompare(b.status || '');
      } else if (sortField === 'rack') {
        comparison = (a.rack_name || '').localeCompare(b.rack_name || '');
      } else if (sortField === 'cpu_name') {
        comparison = (a.cpu_name || a.cpu || '').localeCompare(b.cpu_name || b.cpu || '');
      } else if (sortField === 'ports') {
        const count = itm => ['port1','port2','port3','port4'].filter(p => itm[p] && itm[p] !== '0').length;
        comparison = count(a) - count(b);
      } else if (sortField === 'storage') {
        comparison = calculateTotalStorage(a) - calculateTotalStorage(b);
      } else {
        // default ID
        comparison = (a.id || 0) - (b.id || 0);
      }
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  };

  // Generate inventory stats cards
  const generateInventoryStatsCards = () => {
    // Calculate available servers (status === 'Available')
    const availableServers =
      (dedicatedServers.filter(s => s.status === 'Available').length || 0) +
      (bladeServers.filter(s => s.status === 'Available').length || 0);

    const stats = [
      {
        title: 'Total Servers',
        value: (inventoryStats.totalDedicated + inventoryStats.totalBlades) || '0',
        icon: <Server className="text-indigo-700" size={40} strokeWidth={2} />, 
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Available Servers',
        value: availableServers,
        icon: <CheckCircle className="text-success" size={40} strokeWidth={2} />, 
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'In use',
        value: inventoryStats.inuseServers || '0',
        icon: <Activity className="text-warning" size={40} strokeWidth={2} />, 
        iconClass: 'icon-dropshadow-warning'
      },
      {
        title: 'Defect',
        value: inventoryStats.defectServers || '0',
        icon: <AlertTriangle className="text-danger" size={40} strokeWidth={2} />, 
        iconClass: 'icon-dropshadow-danger'
      }
    ];

    return stats.map((stat, index) => (
      <div
        key={index}
        className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card"
      >
        <div>
          <div className="text-sm text-gray-700">{stat.title}</div>
          <div className="text-2xl font-bold mt-1">{stat.value}</div>
        </div>
        <div className={`card-custom-icon ${stat.iconClass}`}>
          {stat.icon}
        </div>
      </div>
    ));
  };

  const renderDedicatedServersTable = () => {
    const sortedItems = getSortedItems();

    return (
      <table className="w-full">
        <thead>
          <tr className="text-gray-500 text-xs border-b">
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
              ID {getSortIcon('id')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('label')}>
              LABEL {getSortIcon('label')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('cpu_name')}>
  CPU {getSortIcon('cpu_name')}
</th>
            <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('location')}>
              LOCATION {getSortIcon('location')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('rack')}>
              RACK {getSortIcon('rack')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('storage')}>
              STORAGE {getSortIcon('storage')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('ports')}>
              PORTS {getSortIcon('ports')}
            </th>

            <th className="p-4 text-left font-medium">
              ORDER
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
              STATUS {getSortIcon('status')}
            </th>
            <th className="p-4 text-left font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan="9" className="p-4 text-center">
                <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                Loading servers...
              </td>
            </tr>
          ) : error ? (
            <tr>
              <td colSpan="9" className="p-4 text-center text-red-600">
                {error}
              </td>
            </tr>
          ) : sortedItems.length > 0 ? (
            sortedItems.map((server, index) => (
              <tr
                key={server.id}
                className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-50`}
                onClick={() => handleItemClick(server)}
              >
                <td className="p-4 text-gray-700">#{server.id}</td>
                <td className="p-4 font-medium text-indigo-700">{server.label}</td>
                <td className="p-4 text-gray-700 hide-xs">{server.cpu_name || server.cpu || 'Not specified'}</td>
                <td className="p-4 text-gray-700 hide-sm">{server.city_name} ({server.datacenter}), {server.country_name} <span>{getCountryFlag(server.country_name)}</span></td>
                <td className="p-4 text-gray-700 hide-xs">{server.rack_name} ({server.position})</td>
                <td className="p-4 text-gray-700 hide-xs" onMouseEnter={(e)=>showStorageModal(e, server)} onMouseLeave={hideStorageModal}>{formatStorage(calculateTotalStorage(server))}</td>
                <td className="p-4 text-gray-700 hide-xs">{['port1','port2','port3','port4'].filter(p => server[p] && server[p] !== '0').length}</td>
                <td className="p-4">{renderStatusBadge(server.status)}</td>
                <td className="p-4">
                  {server.order_id ? (
                    <div>
                      <a
                        href={`/admin/orders/${server.order_id}`}
                        className="text-indigo-700 hover:underline"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigateTo(`/admin/orders/${server.order_id}`);
                        }}
                      >
                        Order #{server.order_id}
                      </a>
                      {server.client_id && (
                        <div className="text-sm">
                          <a
                            href={`/admin/accounts/${server.client_id}`}
                            className="text-gray-600 hover:underline flex items-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigateTo(`/admin/accounts/${server.client_id}`);
                            }}
                          >
                            <User className="w-3 h-3 mr-1" />
                            {server.company_name || server.client_name}
                          </a>
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">Unassigned</span>
                  )}
                </td>
                <td className="p-4">
  <div className="flex">
    <button
      className="ml-4 p-1 text-gray-500 hover:text-red-700 transition-colors"
      onClick={(e) => {
        e.stopPropagation();
        handleDeleteItem(server.id); // Changed from chassis.id to server.id
      }}
    >
      <Trash2 className="w-4 h-4" />
    </button>
  </div>
</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="9" className="p-4 text-center text-gray-500">
                No servers found matching your criteria
              </td>
            </tr>
          )}
        </tbody>
      </table>
    );
  };

  const renderBladeServersTable = () => {
    const sortedItems = getSortedItems();

    return (
      <table className="w-full">
        <thead>
          <tr className="text-gray-500 text-xs border-b">
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
              ID {getSortIcon('id')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('label')}>
              LABEL {getSortIcon('label')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('cpu_name')}>
              CPU {getSortIcon('cpu_name')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('city_name')}>
              LOCATION {getSortIcon('city_name')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('rack_name')}>
              RACK {getSortIcon('rack_name')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('storage')}>
              STORAGE {getSortIcon('storage')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('ports')}>
              PORTS {getSortIcon('ports')}
            </th>

            <th className="p-4 text-left font-medium">
              ORDER
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
              STATUS {getSortIcon('status')}
            </th>
            <th className="p-4 text-left font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan="9" className="p-4 text-center">
                <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                Loading blade servers...
              </td>
            </tr>
          ) : error ? (
            <tr>
              <td colSpan="9" className="p-4 text-center text-red-600">
                {error}
              </td>
            </tr>
          ) : sortedItems.length > 0 ? (
            sortedItems.map((blade, index) => (
              <tr
                key={blade.id}
                className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-50`}
                onClick={() => handleItemClick(blade)}
              >
                <td className="p-4 text-gray-700">#{blade.id}</td>
                <td className="p-4 font-medium text-indigo-700">{blade.label}</td>
                <td className="p-4 text-gray-700 hide-xs">{blade.cpu_name || blade.cpu || 'Not specified'}</td>
                <td className="p-4 text-gray-700 hide-sm">
  {(() => {
    // Try to get location info from the blade itself first
    let cityName = blade.city_name;
    let datacenter = blade.datacenter;
    let countryName = blade.country_name;

    // If blade doesn't have location info but has chassis_id, get from chassis
    if (blade.chassis_id) {
      const associatedChassis = chassis.find(c => c.id.toString() === blade.chassis_id.toString());
      if (associatedChassis) {
        // Use chassis location if blade doesn't have it
        cityName = cityName || associatedChassis.city_name;
        countryName = countryName || associatedChassis.country_name;

        // Try to get datacenter from chassis directly or use getDatacenterName
        if (!datacenter) {
          datacenter = associatedChassis.datacenter ||
                      (associatedChassis.city_id ? getDatacenterName(associatedChassis.city_id) : '');
        }
      }
    }

    // If still no datacenter but we have city_id, use getDatacenterName
    if (!datacenter && blade.city_id) {
      datacenter = getDatacenterName(blade.city_id);
    }

    return (
      <>
        {cityName || 'Unknown'} {datacenter ? `(${datacenter})` : ''}, {countryName || 'Unknown'}
        <span>{countryName ? getCountryFlag(countryName) : ''}</span>
      </>
    );
  })()}
</td>
                <td className="p-4 text-gray-700 hide-xs">
                  {blade.rack_name ? `${blade.rack_name} (${blade.rack_position})` : 'N/A'}
                </td>
                <td className="p-4 text-gray-700 hide-xs" onMouseEnter={(e)=>showStorageModal(e, blade)} onMouseLeave={hideStorageModal}>{formatStorage(calculateTotalStorage(blade))}</td>
                <td className="p-4 text-gray-700 hide-xs">{['port1','port2','port3','port4'].filter(p => blade[p] && blade[p] !== '0').length}</td>
                <td className="p-4 text-gray-700">
                  {blade.order_id ? (
                    <div>
                      <a
                        href={`/admin/orders/${blade.order_id}`}
                        className="text-indigo-700 hover:underline"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigateTo(`/admin/orders/${blade.order_id}`);
                        }}
                      >
                        Order #{blade.order_id}
                      </a>
                      {blade.client_id && (
                        <div className="text-sm">
                          <a
                            href={`/admin/accounts/${blade.client_id}`}
                            className="text-gray-600 hover:underline flex items-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigateTo(`/admin/accounts/${blade.client_id}`);
                            }}
                          >
                            <User className="w-3 h-3 mr-1" />
                            {blade.company_name || blade.client_name}
                          </a>
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">Unassigned</span>
                  )}
                </td>
                <td className="p-4">{renderStatusBadge(blade.status || 'Active')}</td>
                <td className="p-4">
  <div className="flex">
    <button
      className="ml-4 p-1 text-gray-500 hover:text-red-700 transition-colors"
      onClick={(e) => {
        e.stopPropagation();
        // Force id to integer and log it for verification
        const bladeId = parseInt(blade.id);
        console.log("Deleting blade server with ID:", bladeId, "Original type:", typeof blade.id);
        handleDeleteBladeServer(bladeId);
      }}
    >
      <Trash2 className="w-4 h-4" />
    </button>
  </div>
</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="9" className="p-4 text-center text-gray-500">
                No blade servers found matching your criteria
              </td>
            </tr>
          )}
        </tbody>
      </table>
    );
  };

  const renderChassisTable = () => {
    const sortedItems = getSortedItems();

    return (
      <table className="w-full">
        <thead>
          <tr className="text-gray-500 text-xs border-b">
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
              ID {getSortIcon('id')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('label')}>
              LABEL {getSortIcon('label')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('location')}>
              LOCATION {getSortIcon('location')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('rack')}>
              RACK {getSortIcon('rack')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('ports')}>
              PORTS {getSortIcon('ports')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
              STATUS {getSortIcon('status')}
            </th>
            <th className="p-4 text-left font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan="7" className="p-4 text-center">
                <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                Loading chassis...
              </td>
            </tr>
          ) : error ? (
            <tr>
              <td colSpan="7" className="p-4 text-center text-red-600">
                {error}
              </td>
            </tr>
          ) : sortedItems.length > 0 ? (
            sortedItems.map((chassis, index) => (
              <tr
                key={chassis.id}
                className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-50`}
                onClick={() => handleItemClick(chassis)}
              >
                <td className="p-4 text-gray-700">#{chassis.id}</td>
                <td className="p-4 font-medium text-indigo-700">{chassis.label}</td>
                <td className="p-4 text-gray-700 hide-sm">{chassis.city_name} {getDatacenterName(chassis.city_id) ? `(${getDatacenterName(chassis.city_id)})` : ''}, {chassis.country_name}<span>{getCountryFlag(chassis.country_name)}</span></td>
                <td className="p-4 text-gray-700 hide-xs">{chassis.rack_name} ({chassis.position})</td>
                <td className="p-4 text-gray-700 hide-xs">{['port1','port2','port3','port4'].filter(p => chassis[p] && chassis[p] !== '0').length}</td>
                <td className="p-4">{renderStatusBadge(chassis.status)}</td>
                <td className="p-4">
  <div className="flex">
    <button
      className="ml-4 p-1 text-gray-500 hover:text-red-700 transition-colors"
      onClick={(e) => {
        e.stopPropagation();
        handleDeleteItem(chassis.id);
      }}
    >
      <Trash2 className="w-4 h-4" />
    </button>
  </div>
</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="7" className="p-4 text-center text-gray-500">
                No chassis found matching your criteria
              </td>
            </tr>
          )}
        </tbody>
      </table>
    );
  };

  const renderSwitchesTable = () => {
    const sortedItems = getSortedItems();

    return (
      <table className="w-full">
        <thead>
          <tr className="text-gray-500 text-xs border-b">
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
              ID {getSortIcon('id')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('label')}>
              LABEL {getSortIcon('label')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs">
              IP
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('location')}>
              LOCATION {getSortIcon('location')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('rack')}>
              RACK {getSortIcon('rack')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('storage')}>
              STORAGE {getSortIcon('storage')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('ports')}>
              PORTS {getSortIcon('ports')}
            </th>
            <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
              STATUS {getSortIcon('status')}
            </th>
            <th className="p-4 text-left font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan="7" className="p-4 text-center">
                <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                Loading switches...
              </td>
            </tr>
          ) : error ? (
            <tr>
              <td colSpan="7" className="p-4 text-center text-red-600">
                {error}
              </td>
            </tr>
          ) : sortedItems.length > 0 ? (
            sortedItems.map((switchItem, index) => (
              <tr
                key={switchItem.id}
                className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-50`}
                onClick={() => handleItemClick(switchItem)}
              >
                <td className="p-4 text-gray-700">#{switchItem.id}</td>
                <td className="p-4 font-medium text-indigo-700">{switchItem.label}</td>
                <td className="p-4 text-gray-700 hide-xs">{switchItem.switch_ip}</td>
                <td className="p-4 text-gray-700 hide-sm">
  {switchItem.city_name} {(() => {
    const datacenter = getDatacenterName(switchItem.city_id);
    return datacenter ? `(${datacenter})` : '';
  })()}, {switchItem.country_name}<span>{getCountryFlag(switchItem.country_name)}</span>
</td> <td className="p-4 text-gray-700 hide-xs">{switchItem.rack_name} ({switchItem.rack_position})</td>
                <td className="p-4 text-gray-700 hide-xs">{['port1','port2','port3','port4'].filter(p => switchItem[p] && switchItem[p] !== '0').length}</td>
                <td className="p-4">{renderStatusBadge(switchItem.status)}</td>
                <td className="p-4">
  <div className="flex">
    <button
      className="ml-4 p-1 text-gray-500 hover:text-red-700 transition-colors"
      onClick={(e) => {
        e.stopPropagation();
        handleDeleteItem(switchItem.id); // Changed from chassis.id to switchItem.id
      }}
    >
      <Trash2 className="w-4 h-4" />
    </button>
  </div>
</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="8" className="p-4 text-center text-gray-500">
                No switches found matching your criteria
              </td>
            </tr>
          )}
        </tbody>
      </table>
    );
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Inventory"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Inventory Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Inventory Management</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleOpenAddItemModal(true)}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Bulk Add
                </button>
              </div>
            </div>
          </div>

          {/* Inventory Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {generateInventoryStatsCards()}
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'dedicated'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('dedicated')}
            >
              <Server className="w-4 h-4 inline mr-1" />
              Dedicated Servers
            </button>
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'blade'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('blade')}
            >
              <Layers className="w-4 h-4 inline mr-1" />
              Blade Servers
            </button>
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'chassis'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('chassis')}
            >
              <HardDrive className="w-4 h-4 inline mr-1" />
              Chassis
            </button>
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'switch'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('switch')}
            >
              <Network className="w-4 h-4 inline mr-1" />
              Switches
            </button>
          </div>

          {/* Inventory Table */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusFilter(e.target.value)}
                  >
                    {uniqueStatuses.map(status => (
                      <option key={status} value={status}>{status === 'All' ? 'All Status' : status}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedLocation}
                    onChange={(e) => handleLocationFilter(e.target.value)}
                  >
                    {uniqueLocations.map(location => (
                      <option key={location} value={location}>{location === 'All' ? 'All Locations' : location}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                {/* CPU Filter */}
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedCpuModel}
                    onChange={(e) => handleCpuFilter(e.target.value)}
                  >
                    {uniqueCpus.map(cpu => (
                      <option key={cpu} value={cpu}>{cpu === 'All' ? 'All CPUs' : cpu}</option>
                    ))}
                  </select>
                  <Cpu className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search inventory..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            <div className="overflow-x-auto">
              {selectedTab === 'dedicated' && renderDedicatedServersTable()}
              {selectedTab === 'blade' && renderBladeServersTable()}
              {selectedTab === 'chassis' && renderChassisTable()}
              {selectedTab === 'switch' && renderSwitchesTable()}
            </div>

            {/* Pagination */}
            <div className="p-4 border-t flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="text-sm text-gray-500">
                Showing {getFilteredItems().length} of {
                  selectedTab === 'dedicated' ? dedicatedServers.length :
                  selectedTab === 'blade' ? bladeServers.length :
                  selectedTab === 'chassis' ? chassis.length :
                  switches.length
                } items
              </div>
              <div className="flex space-x-1">
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Previous</button>
                <button className="px-3 py-1 border rounded text-sm bg-indigo-700 text-white">1</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700">2</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Next</button>
              </div>
            </div>
          </div>
        </div>
      </div>





{selectedItem && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-11/12 max-w-6xl flex flex-col max-h-[90vh] mx-4 overflow-hidden">
      {/* Fixed header with rounded top corners */}
      <div className="p-4 border-b flex justify-between items-center bg-gray-50 sticky top-0 z-10 rounded-t-lg">
        <h2 className="text-xl font-bold text-gray-800">
          {isEditMode ? 'Edit' : ''} Item Details
        </h2>
        <button
          onClick={closeItemDetails}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      {/* Scrollable content area */}
      <div className="p-5 overflow-y-auto flex-grow">
        {/* Item Header with Order Info */}
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="h-14 w-14 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 mr-4">
              {selectedTab === 'dedicated' ? <Server className="w-7 h-7" /> :
               selectedTab === 'blade' ? <Layers className="w-7 h-7" /> :
               selectedTab === 'chassis' ? <HardDrive className="w-7 h-7" /> :
               <Network className="w-7 h-7" />}
            </div>
            <div>
              {isEditMode ? (
                <input
                  type="text"
                  name="label"
                  value={selectedItem.label || ''}
                  onChange={handleEditItemChange}
                  className="text-xl font-bold px-2 py-1 border border-gray-300 rounded-md w-full"
                />
              ) : (
                <h3 className="text-xl font-bold">{selectedItem.label}</h3>
              )}
              <div className="text-gray-500 flex items-center">
                <MapPin className="w-4 h-4 mr-1.5" />
                {selectedItem.city_name}, {selectedItem.country_name}<span className="ml-1.5">{getCountryFlag(selectedItem.country_name)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div>
              {isEditMode ? (
                <select
                  name="status"
                  value={selectedItem.status || 'Available'}
                  onChange={handleEditItemChange}
                  className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                >
                          <option value="Available">Available</option>
                          <option value="In Use">In Use</option>
                          <option value="Defect">Defect</option>

                </select>
              ) : (
                renderStatusBadge(selectedItem.status)
              )}
            </div>

            {selectedItem.order_id && (
              <div className="text-right">
                <a
                  href={`/admin/orders/${selectedItem.order_id}`}
                  className="text-indigo-700 hover:underline flex items-center justify-end"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateTo(`/admin/orders/${selectedItem.order_id}`);
                  }}
                >
                  <Calendar className="w-4 h-4 mr-1" />
                  Order #{selectedItem.order_id}
                </a>
                {selectedItem.client_id && (
                  <a
                    href={`/admin/accounts/${selectedItem.client_id}`}
                    className="text-gray-600 hover:underline flex items-center justify-end mt-1"
                    onClick={(e) => {
                      e.preventDefault();
                      navigateTo(`/admin/accounts/${selectedItem.client_id}`);
                    }}
                  >
                    <User className="w-4 h-4 mr-1" />
                    {selectedItem.company_name || selectedItem.client_name}
                  </a>
                )}
              </div>
            )}
          </div>
        </div>

{/* Switch-specific content */}


               {/* Power Management Card - only for dedicated and blade servers */}
        {(selectedTab === 'dedicated' || selectedTab === 'blade') && !isEditMode && (


            <PowerManagement
              server={selectedItem}
              serverType={selectedTab}
              ipmiAddress={selectedItem?.ipmi}
              ipmiRootPassword={selectedItem?.ipmi_root_pass}
              onRefresh={() => {
                // Refresh the server data when power state changes
                if (selectedTab === 'dedicated') {
                  fetchDedicatedServers();
                } else if (selectedTab === 'blade') {
                  fetchBladeServers();
                }
              }}
            />

        )}


        {/* Multi-column Information Layout */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
          {/* Basic Info Card */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Basic Information</h4>
            <div className="space-y-2">
              <div>
                <div className="text-xs text-gray-500">ID</div>
                <div className="font-medium">#{selectedItem.id}</div>
              </div>

              <div>
                <div className="text-xs text-gray-500">Label</div>
                {isEditMode ? (
                  <input
                    type="text"
                    name="label"
                    value={selectedItem.label || ''}
                    onChange={handleEditItemChange}
                    className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  />
                ) : (
                  <div className="font-medium">{selectedItem.label}</div>
                )}
              </div>
              {selectedTab === 'switch' && (
  <div>
    <div className="text-xs text-gray-500">Model</div>
    {isEditMode ? (
      <div className="flex">
        <select
          name="model_id"
          value={selectedItem.model_id || ''}
          onChange={handleEditItemChange}
          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
        >
          <option value="">Select Switch Model</option>
          {switchModels.map(model => (
            <option key={model.id} value={model.id}>{model.name} ({model.size}U)</option>
          ))}
        </select>
        <button
          type="button"
          onClick={() => setShowAddSwitchModelModal(true)}
          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
          title="Add New Switch Model"
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>
    ) : (
      <div className="font-medium flex items-center">
        <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
        {switchModels.find(m => m.id === parseInt(selectedItem.model_id))?.name || 'Not specified'}
        {selectedItem.size_ru && ` (${selectedItem.size_ru}U)`}
      </div>

    )}

  </div>

)}
              {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                <div>
  <div className="text-xs text-gray-500">CPU</div>
  {isEditMode ? (
    <div className="flex">
      <select
        name="cpu"
        value={selectedItem.cpu || ''}
        onChange={handleEditItemChange}
        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
      >
        <option value="">Select CPU Model</option>
        {cpuModels.map(cpu => (
          <option key={cpu.id} value={cpu.id}>{cpu.cpu}</option>
        ))}
      </select>
      <button
        type="button"
        onClick={() => setShowAddCpuModal(true)}
        className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
        title="Add New CPU Model"
      >
        <Plus className="w-4 h-4" />
      </button>
    </div>
  ) : (
    <div className="font-medium">{selectedItem.cpu_name || selectedItem.cpu || 'Not specified'}</div>
  )}
</div>
              )}

              {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                <div>
                  <div className="text-xs text-gray-500">RAM</div>
                  {isEditMode ? (
                    <div className="flex">
                      <select
                        name="ram"
                        value={selectedItem.ram || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select RAM Configuration</option>
                        {ramConfigurations.map(ram => (
                          <option key={ram.id} value={ram.id}>{ram.description}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => setShowAddRamModal(true)}
                        className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                        title="Add New RAM Configuration"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="font-medium">
                      {selectedItem.ram_description ||
                       (selectedItem.ram ? `${selectedItem.ram}GB` : 'Not specified')}
                    </div>
                  )}
                </div>
              )}
{selectedTab === 'dedicated'&& (
                 <div className="mb-3">
                    <div className="text-xs text-gray-500">Size U</div>
                    {isEditMode ? (
                      <input
                        type="text"
                        name="size"
                        value={selectedItem.size || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        placeholder="e.g. ***********"
                      />
                    ) : (
                      <div className="font-medium flex items-center">

                        {selectedItem.size || 'Not assigned'}
                      </div>
                    )}
                  </div>
)}
              <div>
                <div className="text-xs text-gray-500">Status</div>
                {isEditMode ? (
                  <select
                    name="status"
                    value={selectedItem.status || 'Available'}
                    onChange={handleEditItemChange}
                    className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  >
                          <option value="Available">Available</option>
                          <option value="In Use">In Use</option>
                          <option value="Defect">Defect</option>

                  </select>
                ) : (
                  <div className="font-medium">{selectedItem.status}</div>
                )}
              </div>

              {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                <div>

{/* Main Subnet Field */}
<div>
  <div className="text-xs text-gray-500">Main Subnet</div>
  <div className="font-medium flex items-center">
    {selectedItem.main_ip || 'Not assigned'}
    <div className="flex items-center ml-2">
      {selectedItem.main_ip && (
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleUnallocateSubnet(selectedItem.main_ip, true);
          }}
          className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
          title="Unallocate subnet"
        >
          <XCircle className="w-4 h-4" />
        </button>
      )}
      <button
        type="button"
        onClick={() => {
          setIsSelectingMainSubnet(true);
          setIsSubnetSelectionModalOpen(true);
        }}
        className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center mr-1"
        title="Select from available subnets"
        style={{marginLeft:'12px'}}
      >
        <Network className="w-4 h-4" />
      </button>
    </div>
  </div>
</div>

{/* Additional Subnets Field */}
<div>
  <div className="text-xs text-gray-500">Additional Subnets</div>
  <div className="font-medium">
    {selectedItem.additional_ips ? (
      <div className="flex flex-wrap gap-1 items-center">
        {selectedItem.additional_ips.split(',').map((ip, index) => {
          const trimmedIp = ip.trim();
          return trimmedIp ? (
            <div key={index} className="flex items-center">
              <span className="text-sm px-2 py-0.5 bg-gray-100 rounded inline-block mr-1">
                {trimmedIp}
              </span>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleUnallocateSubnet(trimmedIp, false);
                }}
                className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
                title="Unallocate subnet"
              >
                <XCircle className="w-4 h-4" />
              </button>
            </div>
          ) : null;
        }).filter(Boolean)}
        <button
          type="button"
          onClick={() => {
            setIsSelectingMainSubnet(false);
            setIsSubnetSelectionModalOpen(true);
          }}
          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
          title="Add subnet from available subnets"
        >
          <Network className="w-4 h-4" />
        </button>
      </div>
    ) : (
      <div className="flex flex-wrap gap-1">
        <span className="text-gray-500">None assigned </span>
        <button
          type="button"
          onClick={() => {
            setIsSelectingMainSubnet(false);
            setIsSubnetSelectionModalOpen(true);
          }}
          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
          title="Add subnet from available subnets"
        >
          <Network className="w-4 h-4" />
        </button>
      </div>
    )}
  </div>
</div>


                </div>
              )}
            </div>
          </div>

          {/* Location Card */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Location Information</h4>
            <div className="space-y-2">
              <div>
                <div className="text-xs text-gray-500">Datacenter</div>
                {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                  <select
                    name="city_id"
                    value={selectedItem.city_id || ''}
                    onChange={handleEditItemChange}
                    className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="">Select City/Datacenter</option>
                    {cities.map(city => (
                      <option key={city.id} value={city.id}>{city.city} {city.datacenter ? `(${city.datacenter})` : ''}</option>
                    ))}
                  </select>
                ) : (
<div className="font-medium flex items-center">
  <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
  {selectedItem.city_name}
  {(() => {
    // Get datacenter name consistently for all entity types
    const datacenter = selectedItem.datacenter || getDatacenterName(selectedItem.city_id);
    return datacenter ? ` (${datacenter})` : '';
  })()}

  {isEditMode && selectedTab === 'blade' && (
    <span className="ml-2 text-xs text-gray-500 italic">(Location set by chassis)</span>
  )}
</div>
                )}
              </div>

              <div>
                <div className="text-xs text-gray-500">Country</div>
                {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                  <select
                    name="country_id"
                    value={selectedItem.country_id || ''}
                    onChange={handleEditItemChange}
                    className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="">Select Country</option>
                    {countries.map(country => (
                      <option key={country.id} value={country.id}>{country.country}</option>
                    ))}
                  </select>
                ) : (
<div className="font-medium flex items-center">
  <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
  {(() => {
    // For blade servers, check if country info needs to be fetched from chassis
    if (selectedTab === 'blade' && !selectedItem.country_name && selectedItem.chassis_id) {
      const associatedChassis = chassis.find(c => c.id.toString() === selectedItem.chassis_id.toString());
      if (associatedChassis && associatedChassis.country_name) {
        return `${associatedChassis.country_name}`;
      }
    }
    return selectedItem.country_name || 'Unknown';
  })()}
  {isEditMode && selectedTab === 'blade' && (
    <span className="ml-2 text-xs text-gray-500 italic">(Country set by chassis)</span>
  )}
</div>
                )}
              </div>

              <div>
                <div className="text-xs text-gray-500">Rack</div>
                {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                  <div className="flex gap-2">
                    <select
                      name="rack_id"
                      value={selectedItem.rack_id || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-2/3 px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select Rack</option>
                      {racks.map(rack => (
                        <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
                      ))}
                    </select>
                    <input
                      type="text"
                      name={selectedTab === 'switch' ? 'rack_position' : 'position'}
                      value={selectedTab === 'switch' ? selectedItem.rack_position || '' : selectedItem.position || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-1/3 px-2 py-1 border border-gray-300 rounded-md text-sm"
                      placeholder="Position"
                    />
                  </div>
                ) : (
                  <div className="font-medium flex items-center">
                    <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
                    {selectedItem.rack_name}
                    {selectedTab === 'switch' ? `(${selectedItem.rack_position})` :
                     selectedTab === 'blade' ? (() => {
                       if (selectedItem.rack_position) {
                         return `(${selectedItem.rack_position})`;
                       } else if (selectedItem.chassis_id) {
                         const chassisInfo = chassis.find(c => c.id.toString() === selectedItem.chassis_id.toString());
                         return chassisInfo && chassisInfo.position ?
                           `(${chassisInfo.position} - via Chassis)` :
                           '';
                       }
                       return '';
                     })() :
                     selectedItem.position ? `(${selectedItem.position})` : ''}
                    {isEditMode && selectedTab === 'blade' && (
                      <span className="ml-2 text-xs text-gray-500 italic">(Rack set by chassis)</span>
                    )}
                  </div>
                )}
              </div>




              {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                <div>
                  <div>
                    <div className="text-xs text-gray-500">IPMI</div>
                    {isEditMode ? (
                      <CountryIpSelector
                        selectedItem={selectedItem}
                        onChange={handleEditItemChange}
                        name="ipmi"
                        value={selectedItem.ipmi || ''}
                        label=""
                        placeholder="IPMI Address"
                        serverType={selectedTab === 'blade' ? 'blade' : 'dedicated'}
                      />
                    ) : (
                      <div className="font-medium">
                        <div className="flex items-center">
                          <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
                          {selectedItem.ipmi || 'Not specified'}
                        </div>

                        {/* Original IPMI interface access button */}
                        <IpmiAccessField
                          ipmiAddress={selectedItem.ipmi}
                          rootPassword={selectedItem.ipmi_root_pass}
                          isEditMode={isEditMode}
                        />

                        {/* Enhanced iDRAC Console selector with auto-detection */}
                        {selectedItem.ipmi && (
                          <IdracAutoDetectConsole
                            ipmiAddress={selectedItem.ipmi}
                            username="root"
                            password={selectedItem.ipmi_root_pass}
                          />
                        )}
                      </div>
                    )}
                  </div>

                  <PasswordField
                    isEditMode={isEditMode}
                    fieldName="ipmi_root_pass"
                    value={selectedItem.ipmi_root_pass || ''}
                    onChange={(e) => {
                      // Directly update the selectedItem state with the new value
                      const updatedItem = { ...selectedItem, [e.target.name]: e.target.value };
                      setSelectedItem(updatedItem);
                    }}
                    placeholder="••••••••"
                    label="IPMI Root Password"
                  />

                  <PasswordField
                    isEditMode={isEditMode}
                    fieldName="ipmi_user_pass"
                    value={selectedItem.ipmi_user_pass || ''}
                    onChange={(e) => {
                      // Directly update the selectedItem state with the new value
                      const updatedItem = { ...selectedItem, [e.target.name]: e.target.value };
                      setSelectedItem(updatedItem);
                    }}
                    placeholder="••••••••"
                    label="IPMI User Password"
                  />
                </div>
              )}
            </div>
          </div>
          {selectedTab === 'switch' && (
  <>

    {/* Network Settings - basic connectivity */}
    <div className="bg-gray-50 rounded-lg p-4">
      <h5 className="text-sm font-semibold text-gray-700 mb-2 border-b pb-1">Network Settings</h5>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="text-xs text-gray-500">Switch IP</div>
          {isEditMode ? (
            <CountryIpSelector
              selectedItem={selectedItem}
              onChange={handleEditItemChange}
              name="switch_ip"
              value={selectedItem.switch_ip || ''}
              label=""
              placeholder="Select a Switch IP address"
            />
          ) : (
            <div className="font-medium flex items-center">
              <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
              {selectedItem.switch_ip || 'Not configured'}
            </div>
          )}
              <SnmpConfigurationSection
      isEditMode={isEditMode}
      selectedItem={selectedItem}
      handleEditItemChange={handleEditItemChange}
      discoverSwitchPorts={discoverSwitchPorts}
    />
        </div>

        <PasswordField
          isEditMode={isEditMode}
          fieldName="root_password"
          value={selectedItem.root_password || ''}
          onChange={handleEditItemChange}
          placeholder="••••••••"
          label="Root Password"
        />
      </div>
    </div>




  </>
)}
          {/* Network Card for servers */}
          {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Network Information</h4>
              <div className="space-y-4">
              <NetworkConnections
  server={selectedItem}
  serverType={selectedTab === 'blade' ? 'blade' : 'dedicated'}
  onRefresh={() => {
    if (selectedTab === 'dedicated') {
      fetchDedicatedServers();
    } else {
      fetchBladeServers();
    }
  }}
  editable={true} // Allow port assignment in both view and edit modes
  isEditMode={isEditMode} // Pass the edit mode state to control delete button visibility
/>



                <div>
                  <div className="text-xs text-gray-500">MAC Address</div>
                  {isEditMode ? (
                    <input
                      type="text"
                      name="mac"
                      value={selectedItem.mac || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div>
                      <div className="font-medium flex items-center">
                        <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {selectedItem.mac || 'Not specified'}
                      </div>

                      {/* Add MAC Detection Button */}
                      <MacAddressDetectionButton
                        selectedItem={selectedItem}
                        onUpdateMac={handleUpdateMac}
                        isEditMode={isEditMode}
                        serverType={selectedTab}
                      />


                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>


        {/* Type-specific content - blade server */}
        {selectedTab === 'blade' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
            {/* Authentication */}
            <div className="bg-white p-3 rounded shadow-sm">
              <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Authentication</h5>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-xs text-gray-500">User</div>
                  {isEditMode ? (
                    <input
                      type="text"
                      name="user"
                      value={selectedItem.user || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="font-medium flex items-center">
                      <User className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {selectedItem.user || 'Not set'}
                    </div>
                  )}
                </div>
                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="root"
                  value={selectedItem.root}
                  onChange={handleEditItemChange}
                  placeholder="••••••••••"
                  label="Root"
                />
              </div>
            </div>

            {/* Bay Configuration */}
            <div className="bg-white p-3 rounded shadow-sm">
              <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Bay Configuration</h5>
              <StorageDetectionButton
                selectedItem={selectedItem}
                onUpdateStorage={handleUpdateStorage}
                isEditMode={isEditMode}
                serverType="blade"
              />
              <div className="grid grid-cols-5 gap-2">
              {[1, 2, 3, 4, 5, 6, 7, 8].map(bayNum => {
  const bayKey = `bay${bayNum}`;
  const bayNameKey = `${bayKey}_name`;

  // Check if bay exists and is not '0'
  const bayValue = selectedItem[bayKey];
  const bayName = selectedItem[bayNameKey] || bayValue;

  if (!isEditMode) {
    return bayValue && bayValue !== '0' ? (
      <div key={bayNum} className="bg-gray-50 p-2 rounded">
        <div className="text-xs text-gray-500">Bay {bayNum}</div>
        <div className="font-medium text-xs">{bayName}</div>
      </div>
    ) : null;
  } else {
    return (
      <div key={bayNum} className="bg-gray-50 p-2 rounded">
        <div className="text-xs text-gray-500">Bay {bayNum}</div>
        <select
          name={bayKey}
          value={selectedItem[bayKey] || '0'}
          onChange={handleEditItemChange}
          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-xs"
        >
          <option value="0">Not Used</option>
          {storageConfigurations.map(config => (
            <option key={config.id} value={config.id}>{config.label}</option>
          ))}
        </select>
      </div>
    );
  }
})}
              </div>
            </div>
          </div>
        )}

        {/* Storage Configuration for Dedicated Servers */}
        {selectedTab === 'dedicated' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
            {/* Storage Configuration */}
            <div className="bg-white p-3 rounded shadow-sm col-span-full">
              <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Storage Configuration</h5>

              {/* Storage Detection Button */}
              <StorageDetectionButton
                selectedItem={selectedItem}
                onUpdateStorage={handleUpdateStorage}
                isEditMode={isEditMode}
                serverType="dedicated"
              />

              {/* Display bays in a scrollable container */}
              <div className="overflow-auto max-h-96 pr-2">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 mt-3">
                {Array.from({ length: 26 }, (_, i) => i + 1).map(bayNum => {
  const bayKey = `bay${bayNum}`;
  const bayNameKey = `${bayKey}_name`;

  // Check if bay exists and is not '0'
  const bayValue = selectedItem[bayKey];
  const bayName = selectedItem[bayNameKey] || (
    bayValue && storageConfigurations.find(config =>
      String(config.id) === String(bayValue)
    )?.label
  );

  if (!isEditMode) {
    return bayValue && bayValue !== '0' ? (
      <div key={bayNum} className="bg-gray-50 p-2 rounded">
        <div className="text-xs text-gray-500">Bay {bayNum}</div>
        <div className="font-medium text-xs flex items-center">
          <HardDrive className="w-3 h-3 mr-1 text-indigo-700" />
          {bayName || `Drive ${bayValue}`}
        </div>
      </div>
    ) : null;
  } else {
    return (
      <div key={bayNum} className="bg-gray-50 p-2 rounded">
        <div className="text-xs text-gray-500">Bay {bayNum}</div>
        <select
          name={bayKey}
          value={selectedItem[bayKey] || '0'}
          onChange={handleEditItemChange}
          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-xs"
        >
          <option value="0">Not Used</option>
          {storageConfigurations.map(config => (
            <option key={config.id} value={config.id}>{config.label}</option>
          ))}
        </select>
      </div>
    );
  }
})}
                </div>
              </div>

              {/* Show a summary if any storage is configured */}
              {!isEditMode && (() => {
                const configuredBays = Array.from({ length: 26 }, (_, i) => i + 1)
                  .filter(bayNum => {
                    const bayValue = selectedItem[`bay${bayNum}`];
                    return bayValue && bayValue !== '0';
                  }).length;

                return configuredBays > 0 ? (
                  <div className="mt-3 text-xs text-gray-600">
                    {configuredBays} of 26 bays configured
                  </div>
                ) : (
                  <div className="mt-3 text-xs text-gray-500 italic">
                    No storage bays configured yet. Use the Auto-Detect Storage button or edit manually.
                  </div>
                );
              })()}
            </div>
          </div>
        )}

        {/* Chassis Bay Information */}
        {selectedTab === 'chassis' && (
          <div className="bg-white p-3 rounded shadow-sm mb-5">
            <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Bay Configuration</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {[1, 2, 3, 4].map(bayNum => {
                const bayKey = `bay${bayNum}`;
                const bladeId = selectedItem[bayKey];

                if (!isEditMode) {
                  // View mode
                  if (!bladeId || bladeId === "0") {
                    return (
                      <div key={bayNum} className="bg-gray-50 p-2 rounded">
                        <div className="text-xs text-gray-500">Bay {bayNum}</div>
                        <div className="font-medium text-gray-400 flex items-center">
                          <Layers className="w-4 h-4 mr-1.5 text-gray-400" />
                          Unassigned
                        </div>
                      </div>
                    );
                  }

                  // Find the blade server by ID from our loaded bladeServers array
                  const bladeServer = bladeServers.find(blade => blade.id.toString() === bladeId.toString());
                  const bladeLabel = bladeServer ? bladeServer.label : `Blade ${bladeId}`;

                  return (
                    <div key={bayNum} className="bg-gray-50 p-2 rounded">
                      <div className="text-xs text-gray-500">Bay {bayNum}</div>
                      <div className="font-medium flex items-center">
                        <Layers className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {bladeLabel}
                      </div>
                    </div>
                  );
                } else {
                  // Edit mode
                  return (
                    <div key={bayNum} className="bg-gray-50 p-2 rounded">
                      <div className="text-xs text-gray-500">Bay {bayNum}</div>
                      <select
                        name={bayKey}
                        value={selectedItem[bayKey] || '0'}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="0">Unassigned</option>
                        {bladeServers
                          // Show only blades that are not yet assigned to any chassis OR
                          // the blade that is currently assigned to this bay. This prevents
                          // already-allocated blades from appearing in the dropdown while
                          // still keeping the current selection visible.
                          .filter(blade => {
                            const isCurrentlySelected =
                              selectedItem[bayKey] &&
                              blade.id.toString() === selectedItem[bayKey].toString();

                            // blade.chassis_id may be null, 0, '0', or undefined when unassigned
                            const isUnassigned =
                              !blade.chassis_id ||
                              blade.chassis_id === 0 ||
                              blade.chassis_id === '0';

                            return isCurrentlySelected || isUnassigned;
                          })
                          .map(blade => (
                            <option key={blade.id} value={blade.id}>{blade.label}</option>
                          ))}
                      </select>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        )}
{selectedTab === 'switch' && (
  !isEditMode && (
    <div className="bg-white p-3 rounded shadow-sm mb-5">
      <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Switch Ports</h5>
      <div className="text-xs text-gray-500 mb-2">
        Manage ports for this switch. Click "Select Port" to assign a port to a server.
      </div>

      {/* For individual port assignment from switch view */}
      <SwitchPortsManager
        key={`switch-ports-${selectedItem.id}-${portsRefreshKey}`} // Add this key prop
        switchId={selectedItem.id}
        onPortSelected={(port) => {
          console.log("Selected port:", port);
          // Implement port assignment logic here or open assignment modal
        }}
        onAddPort={() => setShowAddPortModal(true)}
      />
    </div>
  )
)}
        {/* Notes */}
        <div className="bg-white p-3 rounded shadow-sm mb-4">
          <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Notes</h5>
          {isEditMode ? (
            <textarea
              name="notes"
              value={selectedItem.notes || ''}
              onChange={handleEditItemChange}
              rows="3"
              className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            ></textarea>
          ) : (
            <div className="text-sm text-gray-700 whitespace-pre-line">{selectedItem.notes || 'No notes available'}</div>
          )}
        </div>
      </div>


      {/* Fixed footer with action buttons */}
      <div className="p-4 border-t bg-white sticky bottom-0 z-10">
        <div className="flex flex-wrap gap-3 justify-end">
          {!isEditMode ? (
            <>
    {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
      <button
        className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
        onClick={() => {
          // Close the modal
          closeItemDetails();
          // Navigate to the server details page
          navigateTo(`/admin/inventory/${selectedTab}/${selectedItem.id}`);
        }}
      >
        <Eye className="w-4 h-4 mr-1" />
        View Details
      </button>
    )}
    {selectedTab === 'switch' && (
      <button
        className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
        onClick={() => {
          // Close the modal
          closeItemDetails();
          // Navigate to the switch details page
          navigateTo(`/admin/inventory/switch/${selectedItem.id}`);
        }}
      >
        <Eye className="w-4 h-4 mr-1" />
        View Switch
      </button>
    )}


              <button
                className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                onClick={() => {
                  setIsEditMode(true);
                  setEditItem({...selectedItem});
                }}
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </button>
            </>
          ) : (
            <>
              <button
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                onClick={() => {
                  setIsEditMode(false);
                  // Reset to original item
                  setSelectedItem({...selectedItem});
                }}
              >
                <X className="w-4 h-4 mr-1" />
                Cancel
              </button>

              <button
                className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                onClick={() => {
                  handleUpdateItem();
                  setIsEditMode(false);
                }}
              >
                <Save className="w-4 h-4 mr-1" />
                Save Changes
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  </div>
)}



      {/* Bulk Server Addition Modal */}
      {bulkAddModalOpen && (
        <BulkServerAddition
          key={`bulk-add-${selectedTab}`}
          isOpen={bulkAddModalOpen}
          onClose={() => setBulkAddModalOpen(false)}
          deviceType={selectedTab}
          onAddItems={handleBulkAddItems}
          cities={cities}
          racks={racks}
          chassis={chassis}
          switches={switches}
          storageConfigurations={storageConfigurations}
        />
      )}


{showAddCpuModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-md shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New CPU Model</h3>
        <button
          onClick={() => {
            setShowAddCpuModal(false);
            setNewCpuModel('');
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">CPU Model Name</label>
        <input
          type="text"
          value={newCpuModel}
          onChange={(e) => setNewCpuModel(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. Intel Xeon E5-2680 v4 (14 cores, 2.4GHz)"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddCpuModal(false);
            setNewCpuModel('');
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddCpuModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add CPU Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}

{/* Add RAM Configuration Modal */}
{showAddRamModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-md shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New RAM Configuration</h3>
        <button
          onClick={() => {
            setShowAddRamModal(false);
            setNewRamConfig({ size: '', description: '' });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">RAM Size (GB)</label>
        <input
          type="number"
          value={newRamConfig.size}
          onChange={(e) => setNewRamConfig({...newRamConfig, size: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 64"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <input
          type="text"
          value={newRamConfig.description}
          onChange={(e) => setNewRamConfig({...newRamConfig, description: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 64GB DDR4"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddRamModal(false);
            setNewRamConfig({ size: '', description: '' });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddRamConfig}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add RAM Configuration
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}

{/* Add Switch Model Modal */}
{showAddSwitchModelModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-md shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New Switch Model</h3>
        <button
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Switch Model Name</label>
        <input
          type="text"
          value={newSwitchModel.name}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, name: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. Cisco Nexus 9396TX"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
        <input
          type="number"
          min="1"
          max="10"
          value={newSwitchModel.size}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, size: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 1"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddSwitchModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add Switch Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}
{isSubnetSelectionModalOpen && (
  <SelectSubnetModal
    onClose={() => {
      setIsSubnetSelectionModalOpen(false);
    }}
    onSelectSubnet={handleSubnetSelection}
    isMainSubnet={isSelectingMainSubnet}
    currentValue={isSelectingMainSubnet
      ? selectedItem?.main_ip
      : selectedItem?.additional_ips}
    serverType={selectedTab}
    serverId={selectedItem?.id}
  />
)}

{/* Add Port Modal */}
{showAddPortModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
      <h3 className="text-lg font-bold mb-4">Add Port Manually</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Port Number</label>
          <input
            type="text"
            placeholder="e.g., Gi1/0/1 or 1"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            value={newPortData.port_number}
            onChange={(e) => setNewPortData({ ...newPortData, port_number: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Port Name / Description</label>
          <input
            type="text"
            placeholder="e.g., Uplink to Core"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            value={newPortData.port_name}
            onChange={(e) => setNewPortData({ ...newPortData, port_name: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Max Speed (Mbps)</label>
          <select
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            value={newPortData.max_speed}
            onChange={(e) => setNewPortData({ ...newPortData, max_speed: e.target.value })}
          >
            {getSpeedOptions().map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="flex justify-end space-x-2 mt-6">
        <button
          className="px-3 py-1.5 border border-gray-300 bg-white rounded-md text-gray-700 text-sm hover:bg-gray-50"
          onClick={() => setShowAddPortModal(false)}
          disabled={addingPort}
        >
          Cancel
        </button>
        <button
          className={`px-3 py-1.5 rounded-md text-white text-sm flex items-center
            ${addingPort ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}
          onClick={handleAddPortManually}
          disabled={addingPort}
        >
          {addingPort ? (
            <>
              <RefreshCw className="w-4 h-4 mr-1.5 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-1.5" />
              Add Port
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}

{toastMessage && (
  <div className={`fixed top-4 right-4 px-4 py-3 rounded-md shadow-lg z-50 flex items-center max-w-sm ${
    toastType === 'success' 
      ? 'bg-green-500 text-white' 
      : 'bg-red-500 text-white'
  }`}>
    {toastType === 'success' ? (
      <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
    ) : (
      <XCircle className="w-5 h-5 mr-2 flex-shrink-0" />
    )}
    <span className="text-sm font-medium">{toastMessage}</span>
  </div>
)}

{/* Storage hover modal */}
{storageModalData && (
  <div
    className="bg-white border shadow-lg rounded px-3 py-2 text-xs"
    style={{ position: 'absolute', left: storageModalData.x, top: storageModalData.y, transform: 'translate(-50%, 0)', zIndex: 9999 }}
    onMouseEnter={() => setStorageModalData(storageModalData)}
    onMouseLeave={hideStorageModal}
  >
    <div className="font-semibold mb-1">Disks</div>
    {storageModalData.disks.map(d => (
      <div key={d.bay} className="whitespace-nowrap">Bay {d.bay}: {d.label}</div>
    ))}
  </div>
)}
    </div>

  );
};

export default InventoryPage;