elseif ($_GET["f"] == "manage_dedicated") {
    $user_context = get_acting_user_id('services');
    if (!$user_context) {
        throw new Exception("Authentication failed");
    }
    
    $user_id = $user_context['acting_user_id'];
    $logged_in_user_id = $user_context['logged_in_user_id'];
    
    // Check if user has permission to access services
if (!$user_context['has_permission']) {
    header("Content-Type: application/json");
    // Return 200 status but with error payload
    echo json_encode([
        "error" => "access_denied",
        "error_code" => 403,
        "message" => "You do not have permission to access services",
        "success" => false
    ]);
    exit;
}
    $user_id = auth_user();
    $dedicated_id = $_GET["id"];

    // Initialize output array
    $output_array = [];

    // Check if auto_renewal column exists in orders_items table
    $columnCheck = $pdo->prepare(
        "SHOW COLUMNS FROM orders_items LIKE 'auto_renewal'"
    );
    $columnCheck->execute();
    if ($columnCheck->rowCount() == 0) {
        // Add auto_renewal column if it doesn't exist
        $pdo->exec(
            "ALTER TABLE orders_items ADD COLUMN auto_renewal tinyint(1) DEFAULT '1'"
        );
        error_log("Added missing auto_renewal column to orders_items table");
    }

    // Check if use_credit column exists in orders_items table
    $columnCheck = $pdo->prepare(
        "SHOW COLUMNS FROM orders_items LIKE 'use_credit'"
    );
    $columnCheck->execute();
    if ($columnCheck->rowCount() == 0) {
        // Add use_credit column if it doesn't exist
        $pdo->exec(
            "ALTER TABLE orders_items ADD COLUMN use_credit tinyint(1) DEFAULT '1'"
        );
        error_log("Added missing use_credit column to orders_items table");
    }

    try {
        // Check if this is an inventory dedicated server (ID >= 1000000)
        if ($dedicated_id >= 1000000) {
            // Query inventory_dedicated_servers with ownership check
            $sth = $pdo->prepare("
              SELECT
                  ids.*,
                  oi.hostname,
                  dc.cpu as cpu_name,
                  dr.size as ram_value,
                  c.city,
                  c.datacenter,
                  co.country,
                  o.recurring_price,
                  o.expiration_date,
                  oi.status as status,
                  o.label as order_label,
                  dos.os_name,
                  dos.logo_url as os_logo_url,
                  r.rack_name,
                  subnt.subnet as subnet_address,
                  oi.storage_id,
                  ds.name as storage_name,
                  oi.due_date,
                  oi.payment_period,
                  oi.requirement_price,
                  oi.auto_renewal,
                  oi.use_credit,
                  ids.main_ip,
                  o.bandwidth_id,
                  u.country as user_country,
                  oi.password
              FROM inventory_dedicated_servers ids
              LEFT JOIN dedicated_cpu dc ON ids.cpu = dc.id
              LEFT JOIN ram_configurations dr ON ids.ram = dr.id
              LEFT JOIN cities c ON ids.city_id = c.id
              LEFT JOIN countries co ON ids.country_id = co.id
              LEFT JOIN orders o ON ids.order_id = o.id
              LEFT JOIN orders_items oi ON oi.server_id = ids.id
              LEFT JOIN dedicated_os dos ON o.os_id = dos.id
              LEFT JOIN racks r ON ids.rack_id = r.id
              LEFT JOIN subnets subnt ON subnt.assigned_server_id = ids.id
              LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
              LEFT JOIN users u ON oi.user_id = u.id
              WHERE ids.id = :dedicated_id AND oi.user_id = :user_id
          ");
            // Log the query for debugging
            error_log("Dedicated server query: " . $sth->queryString);
            $sth->bindValue(":dedicated_id", $dedicated_id);
            $sth->bindValue(":user_id", $user_id);
            $sth->execute();

            if ($row = $sth->fetch()) {
                // Get storage information
                $storage_info = "N/A";
                if (!empty($row["storage_name"])) {
                    $storage_info = $row["storage_name"];
                } else {
                    // Fallback to getStorageFromBays if no storage_id is found
                    $storage_info = getStorageFromBays($row);
                }

                // Format payment period
                $billing_cycle = "Monthly";
                error_log(
                    "Payment period value: " .
                        $row["payment_period"] .
                        " (type: " .
                        gettype($row["payment_period"]) .
                        ")"
                );

                // Dump all row data for debugging
                error_log("All row data: " . json_encode($row));

                // Debug main_ip from database
                error_log(
                    "main_ip from database: " .
                        ($row["main_ip"] ?: "Not found in database")
                );

                if (!empty($row["payment_period"])) {
                    switch (trim($row["payment_period"])) {
                        case "3":
                            $billing_cycle = "Quarterly";
                            break;
                        case "6":
                            $billing_cycle = "Semi-Annual";
                            break;
                        case "12":
                            $billing_cycle = "Annual";
                            break;
                        default:
                            $billing_cycle = "Monthly";
                    }
                }
                error_log("Billing cycle set to: " . $billing_cycle);

                // Calculate first usable IP from subnet
                $main_ip = "N/A";
                if (!empty($row["subnet_address"])) {
                    $subnet_parts = explode("/", $row["subnet_address"]);
                    if (count($subnet_parts) == 2) {
                        $subnet_ip = $subnet_parts[0];
                        $subnet_mask = intval($subnet_parts[1]);

                        // Parse the IP address
                        $ip_parts = explode(".", $subnet_ip);
                        if (count($ip_parts) == 4) {
                            $last_octet = intval($ip_parts[3]);

                            // Handle special cases for different subnet sizes
                            if ($subnet_mask == 31) {
                                // RFC 3021: /31 networks use both IPs for point-to-point links
                                // For /31, use the second IP (x.x.x.1) as the first usable IP
                                $ip_parts[3] = (string) ($last_octet + 1);
                            } elseif ($subnet_mask == 32) {
                                // /32 is a single host address
                                $ip_parts[3] = (string) $last_octet;
                            } elseif ($subnet_mask == 30) {
                                // /30 has 4 IPs: network, 2 usable, broadcast
                                // For consistency with other subnet sizes, use the second usable IP (subnet+2)
                                $ip_parts[3] = (string) ($last_octet + 2);
                            } else {
                                // For most common subnet sizes (/29, /28, /27, /26, /25, /24, etc.)
                                // First usable host IP is typically subnet+2
                                // (subnet+0 is network, subnet+1 is gateway)
                                $ip_parts[3] = (string) ($last_octet + 2);
                            }

                            $main_ip = implode(".", $ip_parts);
                            error_log(
                                "Calculated main_ip: " .
                                    $main_ip .
                                    " from subnet: " .
                                    $row["subnet_address"] .
                                    " with mask: " .
                                    $subnet_mask
                            );
                        }
                    }
                }

                // Get bandwidth details
                $bandwidth_info = "1Gbps";
                $bandwidth_id = null;

                if (!empty($row["bandwidth_id"])) {
                    try {
                        $bw_stmt = $pdo->prepare(
                            "SELECT id, name, speed FROM dedicated_bandwidth WHERE id = :id"
                        );
                        $bw_stmt->bindValue(":id", $row["bandwidth_id"]);
                        $bw_stmt->execute();

                        if ($bw_row = $bw_stmt->fetch()) {
                            $bandwidth_info =
                                $bw_row["name"] .
                                " " .
                                $bw_row["speed"] .
                                "Gbps";
                            $bandwidth_id = $bw_row["id"];
                        }
                    } catch (Exception $e) {
                        error_log(
                            "Error getting bandwidth details: " .
                                $e->getMessage()
                        );
                    }
                }

                // Get VAT rate for the user's country
                $vat_rate = 0;
                try {
                    // First get the user's country code
                    $user_stmt = $pdo->prepare("
                      SELECT country, exempt_vat
                      FROM users
                      WHERE id = :user_id
                  ");
                    $user_stmt->bindValue(":user_id", $user_id);
                    $user_stmt->execute();

                    if ($user_row = $user_stmt->fetch()) {
                        $country_code = strtolower($user_row["country"]);
                        $exempt_vat = $user_row["exempt_vat"];
                        error_log("User country code: " . $country_code);
                        error_log("Exempt from VAT: " . $exempt_vat);

                        // Map country code to country name
                        $country_name = "";
                        switch ($country_code) {
                            case "ro":
                                $country_name = "Romania";
                                break;
                            case "pl":
                                $country_name = "Poland";
                                break;
                            case "pt":
                                $country_name = "Portugal";
                                break;
                            case "sk":
                                $country_name = "Slovakia";
                                break;
                            case "si":
                                $country_name = "Slovenia";
                                break;
                            case "es":
                                $country_name = "Spain";
                                break;
                            case "se":
                                $country_name = "Sweden";
                                break;
                            default:
                                // Default to Romania if country not found
                                $country_name = "Romania";
                        }

                        error_log("Mapped country name: " . $country_name);

                        // Get VAT rate for the country
                        $vat_stmt = $pdo->prepare("
                          SELECT rate
                          FROM vat_rates
                          WHERE country = :country
                      ");
                        $vat_stmt->bindValue(":country", $country_name);
                        $vat_stmt->execute();

                        if ($vat_row = $vat_stmt->fetch()) {
                            $vat_rate = $vat_row["rate"];
                            error_log("VAT rate found: " . $vat_rate);
                        } else {
                            error_log(
                                "No VAT rate found for country: " .
                                    $country_name
                            );
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error getting VAT rate: " . $e->getMessage());
                }

                // Create server data array
                $server_data = [
                    "id" => $row["id"],
                    "label" =>
                        isset($row["hostname"]) && !empty($row["hostname"])
                            ? $row["hostname"]
                            : $row["label"],
                    "type" => "Dedicated",
                    "location" => getCountryShortCode($row["country"] ?: ""),
                    "locationname" => $row["city"] ?: "Unknown",
                    "datacenter" => $row["datacenter"] ?: "N/A",
                    "cabinet" => $row["rack_name"] ?: "Unknown",
                    "price" => $row["recurring_price"] ?: "0.00",
                    "monthly_price" => $row["requirement_price"] ?: "0.00",
                    "expires" => $row["due_date"]
                        ? explode(" ", $row["due_date"])[0]
                        : ($row["expiration_date"]
                            ? explode(" ", $row["expiration_date"])[0]
                            : "N/A"),
                    "status" => $row["status"],
                    "server_status" => $row["status"],
                    "switchport" => "Active",
                    "subnet" => $row["subnet_address"] ?: "/29",
                    "os" => $row["os_name"] ?: "Ubuntu",
                    "os_logo_url" => $row["os_logo_url"] ?: "ubuntu.png",
                    "bandwidth" => $bandwidth_info,
                    "bandwidth_id" => $bandwidth_id,
                    "cpu" => $row["cpu_name"] ?: "Unknown CPU",
                    "ram" => $row["ram_value"]
                        ? $row["ram_value"] . "GB"
                        : "Unknown",
                    "disks" => $storage_info,
                    "main_ip" => $row["main_ip"] ?: $main_ip,
                    "additional_ips" => $row["additional_ips"] ?: "",
                    "ipmi_ip" => $row["ipmi"],
                    "ipmi_user" => "root",
                    "ipmi_pass" => $row["ipmi_user_pass"],
                    "ipmi_root_pass" => $row["ipmi_root_pass"],
                    "password" => $row["password"],
                    "ipmi_type" => "IPMI",
                    "billing_cycle" => $billing_cycle,
                    "payment_period" => $row["payment_period"],
                    "auto_renewal" => isset($row["auto_renewal"])
                        ? intval($row["auto_renewal"]) === 1
                        : true,
                    "use_credit" => isset($row["use_credit"])
                        ? intval($row["use_credit"]) === 1
                        : true,
                    "country" => $row["user_country"],
                    "vat_rate" => $vat_rate,
                ];

                // Add to output array
                $output_array[] = $server_data;
            }
        }
        // Check if this is a blade server (ID < 1000000)
        else {
            // Query blade_server_inventory with ownership check
            $sth = $pdo->prepare("
              SELECT
                  bsi.*,
                  oi.hostname,
                  dc.cpu as cpu_name,
                  dr.size as ram_value,
                  c.city,
                  c.datacenter,
                  co.country,
                  o.recurring_price,
                  o.expiration_date,
                  oi.status as status,
                  o.label as order_label,
                  dos.os_name,
                  dos.logo_url as os_logo_url,
                  subnt.subnet as subnet_address,
                  r.rack_name,
                  oi.storage_id,
                  ds.name as storage_name,
                  oi.due_date,
                  oi.payment_period,
                  oi.requirement_price,
                  oi.auto_renewal,
                  oi.use_credit,
                  bsi.main_ip,
                  o.bandwidth_id,
                  u.country as user_country,
                  oi.password
              FROM blade_server_inventory bsi
              LEFT JOIN dedicated_cpu dc ON bsi.cpu = dc.id
              LEFT JOIN ram_configurations dr ON bsi.ram = dr.id
              LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
              LEFT JOIN cities c ON ich.city_id = c.id
              LEFT JOIN countries co ON ich.country_id = co.id
              LEFT JOIN orders o ON bsi.order_id = o.id
              LEFT JOIN orders_items oi ON oi.server_id = bsi.id
              LEFT JOIN dedicated_os dos ON o.os_id = dos.id
              LEFT JOIN subnets subnt ON subnt.assigned_server_id = bsi.id
              LEFT JOIN racks r ON ich.rack_id = r.id
              LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
              LEFT JOIN users u ON oi.user_id = u.id
              WHERE bsi.id = :dedicated_id AND oi.user_id = :user_id
          ");
            // Log the query for debugging
            error_log("Blade server query: " . $sth->queryString);
            $sth->bindValue(":dedicated_id", $dedicated_id);
            $sth->bindValue(":user_id", $user_id);
            $sth->execute();

            if ($row = $sth->fetch()) {
                // Get storage information
                $storage_info = "N/A";
                if (!empty($row["storage_name"])) {
                    $storage_info = $row["storage_name"];
                } else {
                    // Fallback to getStorageFromBays if no storage_id is found
                    $storage_info = getStorageFromBays($row);
                }

                // Format payment period
                $billing_cycle = "Monthly";
                error_log(
                    "Payment period value: " .
                        $row["payment_period"] .
                        " (type: " .
                        gettype($row["payment_period"]) .
                        ")"
                );

                // Dump all row data for debugging
                error_log("All row data: " . json_encode($row));

                // Debug main_ip from database
                error_log(
                    "main_ip from database: " .
                        ($row["main_ip"] ?: "Not found in database")
                );

                if (!empty($row["payment_period"])) {
                    switch (trim($row["payment_period"])) {
                        case "3":
                            $billing_cycle = "Quarterly";
                            break;
                        case "6":
                            $billing_cycle = "Semi-Annual";
                            break;
                        case "12":
                            $billing_cycle = "Annual";
                            break;
                        default:
                            $billing_cycle = "Monthly";
                    }
                }
                error_log("Billing cycle set to: " . $billing_cycle);

                // Calculate first usable IP from subnet
                $main_ip = "N/A";
                if (!empty($row["subnet_address"])) {
                    $subnet_parts = explode("/", $row["subnet_address"]);
                    if (count($subnet_parts) == 2) {
                        $subnet_ip = $subnet_parts[0];
                        $subnet_mask = intval($subnet_parts[1]);

                        // Parse the IP address
                        $ip_parts = explode(".", $subnet_ip);
                        if (count($ip_parts) == 4) {
                            $last_octet = intval($ip_parts[3]);

                            // Handle special cases for different subnet sizes
                            if ($subnet_mask == 31) {
                                // RFC 3021: /31 networks use both IPs for point-to-point links
                                // For /31, use the second IP (x.x.x.1) as the first usable IP
                                $ip_parts[3] = (string) ($last_octet + 1);
                            } elseif ($subnet_mask == 32) {
                                // /32 is a single host address
                                $ip_parts[3] = (string) $last_octet;
                            } elseif ($subnet_mask == 30) {
                                // /30 has 4 IPs: network, 2 usable, broadcast
                                // For consistency with other subnet sizes, use the second usable IP (subnet+2)
                                $ip_parts[3] = (string) ($last_octet + 2);
                            } else {
                                // For most common subnet sizes (/29, /28, /27, /26, /25, /24, etc.)
                                // First usable host IP is typically subnet+2
                                // (subnet+0 is network, subnet+1 is gateway)
                                $ip_parts[3] = (string) ($last_octet + 2);
                            }
                            $main_ip = implode(".", $ip_parts);
                            error_log(
                                "Calculated main_ip: " .
                                    $main_ip .
                                    " from subnet: " .
                                    $row["subnet_address"] .
                                    " with mask: " .
                                    $subnet_mask
                            );
                        }
                    }
                }

                // Get bandwidth details
                $bandwidth_info = "1Gbps";
                $bandwidth_id = null;

                if (!empty($row["bandwidth_id"])) {
                    try {
                        $bw_stmt = $pdo->prepare(
                            "SELECT id, name, speed FROM dedicated_bandwidth WHERE id = :id"
                        );
                        $bw_stmt->bindValue(":id", $row["bandwidth_id"]);
                        $bw_stmt->execute();

                        if ($bw_row = $bw_stmt->fetch()) {
                            $bandwidth_info =
                                $bw_row["name"] .
                                " " .
                                $bw_row["speed"] .
                                "Gbps";
                            $bandwidth_id = $bw_row["id"];
                        }
                    } catch (Exception $e) {
                        error_log(
                            "Error getting bandwidth details: " .
                                $e->getMessage()
                        );
                    }
                }

                // Get VAT rate for the user's country
                $vat_rate = 0;
                try {
                    // First get the user's country code
                    $user_stmt = $pdo->prepare("
                      SELECT country, exempt_vat
                      FROM users
                      WHERE id = :user_id
                  ");
                    $user_stmt->bindValue(":user_id", $user_id);
                    $user_stmt->execute();

                    if ($user_row = $user_stmt->fetch()) {
                        $country_code = strtolower($user_row["country"]);
                        $exempt_vat = $user_row["exempt_vat"];
                        error_log("User country code: " . $country_code);
                        error_log("Exempt from VAT: " . $exempt_vat);

                        // Map country code to country name
                        $country_name = "";
                        switch ($country_code) {
                            case "ro":
                                $country_name = "Romania";
                                break;
                            case "pl":
                                $country_name = "Poland";
                                break;
                            case "pt":
                                $country_name = "Portugal";
                                break;
                            case "sk":
                                $country_name = "Slovakia";
                                break;
                            case "si":
                                $country_name = "Slovenia";
                                break;
                            case "es":
                                $country_name = "Spain";
                                break;
                            case "se":
                                $country_name = "Sweden";
                                break;
                            default:
                                // Default to Romania if country not found
                                $country_name = "Romania";
                        }

                        error_log("Mapped country name: " . $country_name);

                        // Get VAT rate for the country
                        $vat_stmt = $pdo->prepare("
                          SELECT rate
                          FROM vat_rates
                          WHERE country = :country
                      ");
                        $vat_stmt->bindValue(":country", $country_name);
                        $vat_stmt->execute();

                        if ($vat_row = $vat_stmt->fetch()) {
                            $vat_rate = $vat_row["rate"];
                            error_log("VAT rate found: " . $vat_rate);
                        } else {
                            error_log(
                                "No VAT rate found for country: " .
                                    $country_name
                            );
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error getting VAT rate: " . $e->getMessage());
                }

                // Create server data array
                $server_data = [
                    "id" => $row["id"],
                    "label" =>
                        isset($row["hostname"]) && !empty($row["hostname"])
                            ? $row["hostname"]
                            : $row["label"],
                    "type" => "Dedicated",
                    "location" => getCountryShortCode($row["country"] ?: ""),
                    "locationname" => $row["city"] ?: "Unknown",
                    "datacenter" => $row["datacenter"] ?: "N/A",
                    "cabinet" =>
                        $row["rack_name"] ?:
                        "Blade Chassis #" . $row["chassis_id"],
                    "price" => $row["recurring_price"] ?: "0.00",
                    "monthly_price" => $row["requirement_price"] ?: "0.00",
                    "expires" => $row["due_date"]
                        ? explode(" ", $row["due_date"])[0]
                        : ($row["expiration_date"]
                            ? explode(" ", $row["expiration_date"])[0]
                            : "N/A"),
                    "status" => $row["status"],
                    "server_status" => $row["status"],
                    "switchport" => "Active",
                    "subnet" => $row["subnet_address"] ?: "/29",
                    "os" => $row["os_name"] ?: "Ubuntu",
                    "os_logo_url" => $row["os_logo_url"] ?: "ubuntu.png",
                    "bandwidth" => $bandwidth_info,
                    "bandwidth_id" => $bandwidth_id,
                    "cpu" => $row["cpu_name"] ?: "Unknown CPU",
                    "ram" => $row["ram_value"]
                        ? $row["ram_value"] . "GB"
                        : "Unknown",
                    "disks" => $storage_info,
                    "main_ip" => $row["main_ip"] ?: $main_ip,
                    "additional_ips" => $row["additional_ips"] ?: "",
                    "ipmi_ip" => $row["ipmi"],
                    "ipmi_user" => "root",
                    "ipmi_pass" => $row["ipmi_user_pass"],
                    "ipmi_root_pass" => $row["ipmi_root_pass"],
                    "password" => $row["password"],
                    "ipmi_type" => "iDRAC",
                    "billing_cycle" => $billing_cycle,
                    "payment_period" => $row["payment_period"],
                    "auto_renewal" => isset($row["auto_renewal"])
                        ? intval($row["auto_renewal"]) === 1
                        : true,
                    "use_credit" => isset($row["use_credit"])
                        ? intval($row["use_credit"]) === 1
                        : true,
                    "country" => $row["user_country"],
                    "vat_rate" => $vat_rate,
                ];

                // Add to output array
                $output_array[] = $server_data;
            }
        }
    } catch (Exception $e) {
        error_log("Error in manage_dedicated: " . $e->getMessage());
    }

    // Log the output array for debugging
    error_log("Output array: " . json_encode($output_array));

    // Debug main_ip values
    foreach ($output_array as $server) {
        error_log(
            "Server ID: " .
                $server["id"] .
                ", main_ip: " .
                $server["main_ip"] .
                ", subnet: " .
                $server["subnet"]
        );
    }

    // Return JSON-encoded output
    header("Content-Type: application/json");
    echo json_encode($output_array);
    exit();
} 